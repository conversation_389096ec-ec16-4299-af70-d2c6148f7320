"""
Mobile Tester - Automated mobile test execution with recording
"""

import glob
import json
import pandas as pd
from typing import Dict, List, Any, Optional
import cv2
import numpy as np
from rich.console import Console
from loguru import logger
import os
import asyncio
from datetime import datetime
import time
from pathlib import Path
import subprocess
import threading
import base64

from utils.config import Config
from utils.android_manager import AndroidManager
from utils.os_detector import OSDetector
from rich.panel import Panel
from rich.text import Text

console = Console()

class MobileTester:
    """Automated mobile test execution with recording and validation"""

    def __init__(self):
        self.config = Config()
        self.os_detector = OSDetector()
        self.android_manager = None
        self.device = None
        self.is_initialized = False
        self.test_results = []
        self.recording = False
        self.current_recording_process = None
        self.current_video_path = None
        self.scenario_count = 0

        # Tooltip system configuration
        self.show_ai_tooltips = self.config.get("SHOW_AI_TOOLTIPS", True)
        self.tooltip_duration = self.config.get("TOOLTIP_DURATION", 3.0)  # seconds
        self.is_first_scenario = True
        self.visual_feedback_enabled = True
        self.highlight_color = "red"
        self.highlight_duration = 0.5  # seconds - reduced for less interference
        self.highlight_enabled = True
        self.simple_highlighting_only = True  # Use only simple, safe highlighting

        # UI State Management
        self.current_page = "unknown"
        self.navigation_history = []
        self.app_package = None
        self.current_app_package = None
        self.is_in_main_app = True
        self.external_app_history = []
        self.page_indicators = {
            "main_page": ["Ruang Murid", "Ruang Guru", "Ruang Belajar"],
            "ruang_murid": ["Sumber Belajar", "Kelas Maya", "Asesmen"],
            "sumber_belajar": ["Portal pembelajaran digital", "Materi", "Video"]
        }
        # Known external app packages that might be opened from main app
        self.external_app_packages = [
            "com.android.chrome",
            "com.android.browser",
            "com.google.android.youtube",
            "com.whatsapp",
            "com.instagram.android",
            "com.facebook.katana",
            "com.google.android.gm",  # Gmail
            "com.google.android.apps.docs",  # Google Docs
            "com.adobe.reader",  # PDF Reader
            "com.google.android.apps.maps"  # Google Maps
        ]

    async def initialize(self):
        """Initialize mobile tester"""
        try:
            console.print("[yellow]Initializing Mobile Tester...[/yellow]")

            # Initialize Android manager
            self.android_manager = AndroidManager()
            await self.android_manager.initialize()

            # Ensure output directories exist
            os.makedirs(self.config.get("SCREENSHOTS_PATH", "./data/screenshots"), exist_ok=True)
            os.makedirs(self.config.get("VIDEOS_PATH", "./data/videos"), exist_ok=True)

            self.is_initialized = True
            console.print("[green]Mobile Tester initialized successfully![/green]")

        except Exception as e:
            console.print(f"[red]Failed to initialize Mobile Tester: {e}[/red]")
            logger.error(f"Mobile Tester initialization failed: {e}", exc_info=True)
            raise

    async def run_tests(self, mobile_os_callback=None, specific_file: str = "") -> Dict[str, Any]:
        """Run automated mobile tests with interactive app opening and analysis integration"""
        from utils.terminal_logger import log_ai_analysis_event, log_detailed_section, log_device_interaction

        try:
            console.print("[yellow]Starting automated mobile testing...[/yellow]")
            log_ai_analysis_event("MOBILE_TEST_INIT", "Initializing mobile test environment")

            # Step 1: Detect OS and mobile platform
            current_os = self.os_detector.detect_os()
            console.print(f"[cyan]Detected OS: {current_os}[/cyan]")
            log_device_interaction("OS_DETECTION", f"Detected operating system: {current_os}")

            mobile_platform = await self._get_mobile_platform(mobile_os_callback)
            log_device_interaction("PLATFORM_SELECTION", f"Selected mobile platform: {mobile_platform}")

            if mobile_platform == "ios":
                console.print("[yellow]iOS testing is currently under development[/yellow]")
                console.print("[cyan]Please select Android for now, or check back in future updates[/cyan]")
                log_ai_analysis_event("MOBILE_TEST_ERROR", "iOS testing not yet supported")
                return {
                    "success": False,
                    "error": "iOS testing is currently under development"
                }

            # Step 2: Setup Android environment (with processing indicator)
            log_ai_analysis_event("ENVIRONMENT_SETUP", "Setting up Android test environment")
            with console.status("[bold green]Processing command..."):
                setup_result = await self._setup_test_environment()
                if not setup_result["success"]:
                    log_ai_analysis_event("ENVIRONMENT_SETUP_FAILED", f"Environment setup failed: {setup_result.get('error', 'Unknown error')}")
                    return setup_result
                log_ai_analysis_event("ENVIRONMENT_SETUP_SUCCESS", "Android test environment setup completed")

            # Step 3: Interactive app opening (WITHOUT processing indicator to allow user input)
            log_ai_analysis_event("APP_OPENING", "Starting interactive app opening process")
            app_opening_result = await self._interactive_app_opening(mobile_os_callback)
            if not app_opening_result["success"]:
                log_ai_analysis_event("APP_OPENING_FAILED", f"App opening failed: {app_opening_result.get('error', 'Unknown error')}")
                return app_opening_result
            log_ai_analysis_event("APP_OPENING_SUCCESS", "Application opened successfully")

            # Step 4-7: Execute tests (with processing indicator)
            log_ai_analysis_event("TEST_EXECUTION", "Beginning test execution phase")
            with console.status("[bold green]Processing command..."):
                # Step 4: Load latest analysis data for element locators
                log_ai_analysis_event("ANALYSIS_DATA_LOAD", "Loading latest analysis data for element locators")
                analysis_data = await self._load_latest_analysis_data()

                # Step 5: Load Gherkin test files
                if specific_file:
                    log_ai_analysis_event("GHERKIN_LOAD", f"Loading specific Gherkin test file: {specific_file}")
                    test_files = await self._load_specific_gherkin_file(specific_file)
                else:
                    log_ai_analysis_event("GHERKIN_LOAD", "Loading all Gherkin test files")
                    test_files = await self._load_gherkin_files()

                if not test_files:
                    if specific_file:
                        log_ai_analysis_event("GHERKIN_LOAD_FAILED", f"Specific Gherkin test file not found: {specific_file}")
                        return {
                            "success": False,
                            "error": f"Gherkin test file '{specific_file}' not found. Please check the filename."
                        }
                    else:
                        log_ai_analysis_event("GHERKIN_LOAD_FAILED", "No Gherkin test files found")
                        return {
                            "success": False,
                            "error": "No Gherkin test files found. Please run /generate-gherkin first."
                        }
                log_ai_analysis_event("GHERKIN_LOAD_SUCCESS", f"Loaded {len(test_files)} Gherkin test files")

                # Step 6: Execute tests with analysis data integration
                log_ai_analysis_event("TEST_EXECUTION_START", f"Executing {len(test_files)} test files")
                test_results = await self._execute_test_files_with_analysis(test_files, analysis_data)

                # Step 7: Update testsuite with results
                log_ai_analysis_event("TESTSUITE_UPDATE", "Updating testsuite with test results")
                await self._update_testsuite_results(test_results)

                # Step 8: Generate test report
                log_ai_analysis_event("REPORT_GENERATION", "Generating test report")
                report_result = await self._generate_test_report(test_results)

                # Step 9: Clean up visual feedback features
                log_ai_analysis_event("CLEANUP", "Cleaning up visual feedback features")
                device_serial = await self._get_device_serial()
                if device_serial:
                    await self._disable_visual_feedback_features(device_serial)

            return {
                "success": True,
                "message": "Mobile tests completed successfully",
                "data": {
                    "tests_executed": len(test_results),
                    "tests_passed": sum(1 for r in test_results if r["status"] == "PASSED"),
                    "tests_failed": sum(1 for r in test_results if r["status"] == "FAILED"),
                    "report_file": report_result.get("report_file")
                }
            }

        except Exception as e:
            logger.error(f"Mobile testing failed: {e}", exc_info=True)
            return {"success": False, "error": str(e)}

    async def _get_mobile_platform(self, mobile_os_callback=None) -> str:
        """Get mobile platform preference"""
        if mobile_os_callback:
            # Use callback to get mobile OS from chat interface
            console.print("[yellow]What mobile platform would you like to test?[/yellow]")
            console.print("1. Android")
            console.print("2. iOS")

            choice = await mobile_os_callback()

            if choice in ['1', 'android']:
                return "android"
            elif choice in ['2', 'ios']:
                return "ios"
            else:
                console.print("[yellow]Invalid choice, defaulting to Android[/yellow]")
                return "android"
        else:
            # Fallback to default behavior (for backward compatibility)
            console.print("[yellow]No mobile platform specified, defaulting to Android[/yellow]")
            return "android"

    async def _setup_test_environment(self) -> Dict[str, Any]:
        """Setup test environment"""
        try:
            console.print("[yellow]Setting up test environment...[/yellow]")

            # Initialize Android manager if not already done
            if not self.android_manager:
                self.android_manager = AndroidManager()
                await self.android_manager.initialize()

            # Connect to device (this will handle emulator startup if needed)
            device_result = await self.android_manager.connect_device()
            if not device_result["success"]:
                return device_result

            self.device = device_result["device"]

            # Initialize visual feedback features for better user visibility
            device_serial = await self._get_device_serial()
            if device_serial:
                await self._enable_visual_feedback_features(device_serial)
                console.print("[green]✅ Visual feedback enabled - you will see red circles when AI performs actions![/green]")
            else:
                console.print("[yellow]⚠️ Could not enable visual feedback - device serial not found[/yellow]")

            console.print("[green]✅ Test environment setup completed![/green]")
            console.print("[cyan]📱 Device ready for non-headless testing - you can see all actions on the emulator![/cyan]")

            return {"success": True}

        except Exception as e:
            logger.error(f"Test environment setup failed: {e}")
            return {"success": False, "error": str(e)}

    async def _load_gherkin_files(self) -> List[str]:
        """Load all Gherkin test files"""
        try:
            gherkin_path = self.config.get("GHERKIN_OUTPUT_PATH")
            pattern = os.path.join(gherkin_path, "*.feature")

            files = glob.glob(pattern)
            console.print(f"[cyan]Found {len(files)} Gherkin test files[/cyan]")

            return files

        except Exception as e:
            logger.error(f"Failed to load Gherkin files: {e}")
            return []

    async def _load_specific_gherkin_file(self, filename: str) -> List[str]:
        """Load a specific Gherkin test file"""
        try:
            gherkin_path = self.config.get("GHERKIN_OUTPUT_PATH")

            # Handle different filename formats
            if not filename.endswith('.feature'):
                filename += '.feature'

            # Check if it's just a filename or includes path
            if os.path.sep not in filename:
                # Just a filename, look in the gherkin output path
                file_path = os.path.join(gherkin_path, filename)
            else:
                # Includes path, use as-is but ensure it's relative to project root
                file_path = filename
                if not os.path.isabs(file_path):
                    # Make it relative to project root
                    file_path = os.path.join(os.getcwd(), file_path)

            # Check if file exists
            if os.path.exists(file_path):
                console.print(f"[green]✅ Found specific Gherkin file: {filename}[/green]")
                return [file_path]
            else:
                # Try to find the file in the gherkin directory
                pattern = os.path.join(gherkin_path, f"*{filename}*")
                matching_files = glob.glob(pattern)

                if matching_files:
                    console.print(f"[green]✅ Found matching Gherkin file: {os.path.basename(matching_files[0])}[/green]")
                    return [matching_files[0]]
                else:
                    console.print(f"[red]❌ Gherkin file not found: {filename}[/red]")
                    console.print(f"[yellow]Searched in: {gherkin_path}[/yellow]")
                    return []

        except Exception as e:
            logger.error(f"Failed to load specific Gherkin file {filename}: {e}")
            return []

    async def _execute_test_files(self, test_files: List[str]) -> List[Dict[str, Any]]:
        """Execute all test files"""
        test_results = []

        for test_file in test_files:
            console.print(f"[cyan]Executing test file: {os.path.basename(test_file)}[/cyan]")

            try:
                # Start recording for this test
                await self._start_recording(test_file)

                # Parse and execute scenarios
                scenarios = await self._parse_gherkin_file(test_file)

                for scenario in scenarios:
                    result = await self._execute_scenario(scenario, test_file)
                    test_results.append(result)

                # Stop recording
                await self._stop_recording()

            except Exception as e:
                logger.error(f"Failed to execute test file {test_file}: {e}")
                test_results.append({
                    "test_file": test_file,
                    "scenario": "File Execution",
                    "status": "FAILED",
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                })

        return test_results

    async def _parse_gherkin_file(self, file_path: str) -> List[Dict[str, Any]]:
        """Parse Gherkin file and extract scenarios"""
        scenarios = []

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Simple Gherkin parsing (in a real implementation, use a proper parser)
            lines = content.split('\n')
            current_scenario = None

            for line in lines:
                line = line.strip()

                if line.startswith('Scenario:'):
                    if current_scenario:
                        scenarios.append(current_scenario)

                    current_scenario = {
                        "name": line.replace('Scenario:', '').strip(),
                        "steps": [],
                        "file": file_path
                    }

                elif current_scenario and line and any(line.startswith(keyword) for keyword in ['Given', 'When', 'Then', 'And']):
                    current_scenario["steps"].append(line)

            # Add the last scenario
            if current_scenario:
                scenarios.append(current_scenario)

            return scenarios

        except Exception as e:
            logger.error(f"Failed to parse Gherkin file {file_path}: {e}")
            return []

    async def _execute_scenario(self, scenario: Dict[str, Any], test_file: str) -> Dict[str, Any]:
        """Execute a single test scenario"""
        from utils.terminal_logger import log_ai_analysis_event, log_device_interaction

        try:
            console.print(f"[yellow]Executing scenario: {scenario['name']}[/yellow]")
            log_ai_analysis_event("SCENARIO_START", f"Starting scenario: {scenario['name']} from {os.path.basename(test_file)}")

            result = {
                "test_file": os.path.basename(test_file),
                "scenario": scenario["name"],
                "status": "PASSED",
                "steps_executed": 0,
                "steps_passed": 0,
                "steps_failed": 0,
                "error": None,
                "timestamp": datetime.now().isoformat(),
                "screenshots": []
            }

            # Execute each step
            for i, step in enumerate(scenario["steps"]):
                log_device_interaction("STEP_EXECUTION", f"Step {i+1}/{len(scenario['steps'])}: {step}")
                step_result = await self._execute_step(step, i)

                result["steps_executed"] += 1

                if step_result["success"]:
                    result["steps_passed"] += 1
                    console.print(f"[green]✓ {step}[/green]")
                    log_device_interaction("STEP_SUCCESS", f"Step {i+1} passed: {step}")
                else:
                    result["steps_failed"] += 1
                    result["status"] = "FAILED"
                    result["error"] = step_result.get("error", "Step failed")
                    console.print(f"[red]✗ {step} - {result['error']}[/red]")
                    log_device_interaction("STEP_FAILURE", f"Step {i+1} failed: {step}", f"Error: {result['error']}")

                    # Take screenshot on failure
                    if self.config.get("SCREENSHOT_ON_FAIL"):
                        screenshot_path = await self._take_screenshot(f"failure_{i}")
                        if screenshot_path:
                            result["screenshots"].append(screenshot_path)
                            log_device_interaction("SCREENSHOT_TAKEN", f"Failure screenshot saved: {screenshot_path}")

                    break  # Stop on first failure

                # Small delay between steps
                await asyncio.sleep(1)

            # Log scenario completion
            if result["status"] == "PASSED":
                log_ai_analysis_event("SCENARIO_SUCCESS", f"Scenario '{scenario['name']}' completed successfully - {result['steps_passed']}/{result['steps_executed']} steps passed")
            else:
                log_ai_analysis_event("SCENARIO_FAILURE", f"Scenario '{scenario['name']}' failed - {result['steps_passed']}/{result['steps_executed']} steps passed, Error: {result['error']}")

            return result

        except Exception as e:
            logger.error(f"Failed to execute scenario {scenario['name']}: {e}")
            return {
                "test_file": os.path.basename(test_file),
                "scenario": scenario["name"],
                "status": "FAILED",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    async def _execute_step(self, step: str, step_index: int) -> Dict[str, Any]:
        """Execute a single test step"""
        try:
            step_lower = step.lower()

            # Parse step and determine action
            if "launch" in step_lower and "application" in step_lower:
                return await self._step_launch_app()

            elif "tap" in step_lower or "click" in step_lower:
                element_text = self._extract_element_from_step(step)
                return await self._step_tap_element(element_text)

            elif "enter" in step_lower and "in" in step_lower:
                text_to_enter = self._extract_text_from_step(step)
                element_text = self._extract_element_from_step(step)
                return await self._step_enter_text(text_to_enter, element_text)

            elif "scroll" in step_lower:
                direction = "down" if "down" in step_lower else "up"
                return await self._step_scroll(direction)

            elif "should see" in step_lower:
                element_text = self._extract_element_from_step(step)
                return await self._step_verify_element(element_text)

            else:
                # Generic validation step
                return await self._step_generic_validation(step)

        except Exception as e:
            logger.error(f"Failed to execute step '{step}': {e}")
            return {"success": False, "error": str(e)}

    async def _step_launch_app(self) -> Dict[str, Any]:
        """Launch the application"""
        try:
            if not self._validate_device():
                return {"success": False, "error": "Device not available"}

            console.print("[cyan]🚀 Launching application...[/cyan]")

            # Get current app or use a default
            current_app = self.device.app_current()
            if current_app and current_app.get("package"):
                console.print(f"[green]✅ Application already running: {current_app.get('package')}[/green]")
                return {"success": True}

            # If no app is running, this step passes (app should already be launched)
            console.print("[green]✅ Application launch step completed[/green]")
            return {"success": True}

        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _step_tap_element(self, element_text: str) -> Dict[str, Any]:
        """Tap on an element (legacy method - use enhanced version for better results)"""
        from utils.terminal_logger import log_device_interaction

        try:
            if not self._validate_device():
                log_device_interaction("TAP_ELEMENT_ERROR", f"Device validation failed for element: {element_text}")
                return {"success": False, "error": "Device not available"}

            if not element_text:
                log_device_interaction("TAP_ELEMENT_ERROR", "No element text specified for tap action")
                return {"success": False, "error": "No element specified"}

            console.print(f"[cyan]👆 Tapping element: '{element_text}'[/cyan]")
            log_device_interaction("TAP_ELEMENT_ATTEMPT", f"Attempting to tap element: '{element_text}'")

            # Try to find element by text
            if self.device(text=element_text).exists:
                element = self.device(text=element_text)

                # Add visual highlight before clicking - pass actual element and text
                await self._add_visual_highlight(element, "CLICK", element_text)

                element.click()
                console.print(f"[green]✅ Successfully tapped element by text: '{element_text}'[/green]")
                log_device_interaction("TAP_ELEMENT_SUCCESS", f"Successfully tapped element by text: '{element_text}'")

                # Extended visual delay to show highlight
                await asyncio.sleep(self.highlight_duration)

                # Highlight element if enabled
                if self.config.get("ELEMENT_HIGHLIGHT"):
                    await self._highlight_element(element)

                return {"success": True}

            # Try by content description
            elif self.device(description=element_text).exists:
                element = self.device(description=element_text)

                # Add visual highlight before clicking - pass actual element and text
                await self._add_visual_highlight(element, "CLICK", element_text)

                element.click()
                console.print(f"[green]✅ Successfully tapped element by description: '{element_text}'[/green]")

                # Extended visual delay to show highlight
                await asyncio.sleep(self.highlight_duration)

                if self.config.get("ELEMENT_HIGHLIGHT"):
                    await self._highlight_element(element)

                return {"success": True}

            else:
                console.print(f"[red]❌ Element '{element_text}' not found[/red]")
                return {"success": False, "error": f"Element '{element_text}' not found"}

        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _step_enter_text(self, text: str, element_text: str) -> Dict[str, Any]:
        """Enter text in an element"""
        try:
            # Find input element
            if self.device(text=element_text).exists:
                element = self.device(text=element_text)
                element.set_text(text)
                return {"success": True}

            elif self.device(description=element_text).exists:
                element = self.device(description=element_text)
                element.set_text(text)
                return {"success": True}

            else:
                return {"success": False, "error": f"Input element '{element_text}' not found"}

        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _step_scroll(self, direction: str) -> Dict[str, Any]:
        """Scroll in specified direction"""
        try:
            if direction == "down":
                self.device.swipe(500, 800, 500, 200)
            else:  # up
                self.device.swipe(500, 200, 500, 800)

            return {"success": True}

        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _step_verify_element(self, element_text: str) -> Dict[str, Any]:
        """Verify element exists"""
        try:
            if not element_text:
                return {"success": True}  # Generic validation

            exists = (
                self.device(text=element_text).exists or
                self.device(description=element_text).exists
            )

            if exists:
                return {"success": True}
            else:
                return {"success": False, "error": f"Element '{element_text}' not visible"}

        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _step_generic_validation(self, step: str) -> Dict[str, Any]:
        """Generic validation step"""
        try:
            # For generic steps, just verify the screen is responsive
            screen_xml = self.device.dump_hierarchy()
            if screen_xml:
                return {"success": True}
            else:
                return {"success": False, "error": "Screen not responsive"}

        except Exception as e:
            return {"success": False, "error": str(e)}

    def _extract_element_from_step(self, step: str) -> str:
        """Extract element text from step"""
        import re

        # Simple extraction - look for text in quotes
        matches = re.findall(r'"([^"]*)"', step)
        if matches:
            return matches[0]

        # Look for "click on 'X'" pattern (single quotes)
        matches = re.findall(r"click on '([^']*)'", step)
        if matches:
            return matches[0].strip()

        # Look for "I should see 'X'" pattern (single quotes)
        matches = re.findall(r"should see '([^']*)'", step)
        if matches:
            return matches[0].strip()

        # Look for "the X element" pattern
        matches = re.findall(r'the ([^"]*?) element', step)
        if matches:
            return matches[0].strip()

        # Look for "the X button should be clickable" pattern
        matches = re.findall(r'the ([^"]*?) button should be clickable', step)
        if matches:
            return matches[0].strip()

        # Look for "X button should be clickable" pattern (but not starting with "And")
        matches = re.findall(r'(?:And )?([^"]*?) button should be clickable', step)
        if matches:
            return matches[0].strip()

        # Look for "the X should be clickable" pattern
        matches = re.findall(r'the ([^"]*?) should be clickable', step)
        if matches:
            return matches[0].strip()

        # Look for "click on X button" pattern (without quotes)
        matches = re.findall(r'click on ([^"]*?) button', step)
        if matches:
            return matches[0].strip()

        return ""

    async def _detect_current_page(self) -> str:
        """Detect current page based on visible elements"""
        try:
            if not self.device:
                return "unknown"

            # Get current screen elements
            screen_xml = self.device.dump_hierarchy()
            if not screen_xml:
                return "unknown"

            # Check for page indicators
            for page_name, indicators in self.page_indicators.items():
                found_indicators = 0
                for indicator in indicators:
                    if (self.device(text=indicator).exists or
                        self.device(description=indicator).exists):
                        found_indicators += 1

                # If we find at least 2 indicators, we're likely on that page
                if found_indicators >= 2:
                    console.print(f"[cyan]📍 Detected current page: {page_name}[/cyan]")
                    return page_name

            # If no specific page detected, try to determine based on single indicators
            for page_name, indicators in self.page_indicators.items():
                for indicator in indicators:
                    if (self.device(text=indicator).exists or
                        self.device(description=indicator).exists):
                        console.print(f"[cyan]📍 Detected current page: {page_name} (single indicator)[/cyan]")
                        return page_name

            return "unknown"

        except Exception as e:
            logger.debug(f"Failed to detect current page: {e}")
            return "unknown"

    async def _detect_current_app(self) -> Dict[str, Any]:
        """Detect current application and whether we're in main app or external app"""
        try:
            if not self.device:
                return {"package": "unknown", "is_main_app": False, "is_external": False}

            # Get current app info
            current_app = self.device.app_current()
            if not current_app:
                return {"package": "unknown", "is_main_app": False, "is_external": False}

            current_package = current_app.get("package", "unknown")
            self.current_app_package = current_package

            # Check if we're in main app
            is_main_app = (current_package == self.app_package)

            # Check if we're in known external app
            is_external_app = current_package in self.external_app_packages

            # Update state
            self.is_in_main_app = is_main_app

            console.print(f"[cyan]📱 Current app: {current_package}[/cyan]")
            console.print(f"[cyan]🏠 Main app: {self.app_package}[/cyan]")
            console.print(f"[cyan]📍 In main app: {is_main_app}[/cyan]")
            console.print(f"[cyan]🌐 In external app: {is_external_app}[/cyan]")

            return {
                "package": current_package,
                "is_main_app": is_main_app,
                "is_external": is_external_app,
                "app_name": current_app.get("activity", "unknown")
            }

        except Exception as e:
            logger.debug(f"Failed to detect current app: {e}")
            return {"package": "unknown", "is_main_app": False, "is_external": False}

    async def _switch_to_main_app(self) -> bool:
        """Switch back to main application"""
        try:
            if not self.app_package:
                console.print("[red]❌ Main app package not set[/red]")
                return False

            console.print(f"[yellow]🔄 Switching to main app: {self.app_package}[/yellow]")

            # Method 1: Use app_start to bring main app to foreground
            self.device.app_start(self.app_package)
            await asyncio.sleep(2)  # Wait for app switch

            # Verify we're back in main app
            app_info = await self._detect_current_app()
            if app_info["is_main_app"]:
                console.print("[green]✅ Successfully switched to main app[/green]")
                self.is_in_main_app = True
                return True

            # Method 2: Try using recent apps and selecting main app
            console.print("[yellow]🔄 Trying recent apps method...[/yellow]")
            self.device.press("recent")
            await asyncio.sleep(1)

            # Look for main app in recent apps and tap it
            # This is a fallback method
            self.device.press("back")  # Close recent apps
            await asyncio.sleep(1)

            # Method 3: Force restart main app
            console.print("[yellow]🔄 Force restarting main app...[/yellow]")
            self.device.app_stop(self.app_package)
            await asyncio.sleep(1)
            self.device.app_start(self.app_package)
            await asyncio.sleep(3)

            # Final verification
            app_info = await self._detect_current_app()
            if app_info["is_main_app"]:
                console.print("[green]✅ Successfully returned to main app via restart[/green]")
                self.is_in_main_app = True
                return True

            console.print("[red]❌ Failed to switch to main app[/red]")
            return False

        except Exception as e:
            logger.error(f"Failed to switch to main app: {e}")
            return False

    async def _navigate_to_main_page(self) -> bool:
        """Navigate back to main page with app awareness"""
        try:
            console.print("[yellow]🏠 Navigating to main page...[/yellow]")

            # Step 1: Detect current app and page
            app_info = await self._detect_current_app()
            current_page = await self._detect_current_page()
            self.current_page = current_page

            # Step 2: Check if we're already on main page in main app
            if app_info["is_main_app"] and current_page == "main_page":
                console.print("[green]✅ Already on main page in main app[/green]")
                return True

            # Step 3: If we're in external app, switch back to main app first
            if not app_info["is_main_app"]:
                console.print(f"[yellow]🌐 Currently in external app: {app_info['package']}[/yellow]")
                console.print("[yellow]🔄 Switching back to main application...[/yellow]")

                # Add to external app history
                self.external_app_history.append({
                    "package": app_info["package"],
                    "timestamp": datetime.now().isoformat(),
                    "from_page": self.current_page
                })

                # Switch to main app
                switch_success = await self._switch_to_main_app()
                if not switch_success:
                    console.print("[red]❌ Failed to switch back to main app[/red]")
                    return False

                # After switching, detect page again
                current_page = await self._detect_current_page()
                self.current_page = current_page

                if current_page == "main_page":
                    console.print("[green]✅ Successfully returned to main page via app switch[/green]")
                    return True

            # Step 4: We're in main app but not on main page - use back button navigation
            if app_info["is_main_app"] and current_page != "main_page":
                console.print("[yellow]📱 In main app but not on main page, using back navigation...[/yellow]")

                max_back_attempts = 3
                for attempt in range(max_back_attempts):
                    console.print(f"[cyan]🔙 Pressing back button (attempt {attempt + 1}/{max_back_attempts})[/cyan]")
                    self.device.press("back")
                    await asyncio.sleep(2)  # Wait for navigation

                    # Check if we're on main page now
                    current_page = await self._detect_current_page()
                    if current_page == "main_page":
                        console.print("[green]✅ Successfully navigated to main page[/green]")
                        self.current_page = "main_page"
                        return True

            # Step 5: Last resort - restart the main app
            console.print("[yellow]🔄 Using app restart as fallback...[/yellow]")
            if self.app_package:
                self.device.app_stop(self.app_package)
                await asyncio.sleep(2)
                self.device.app_start(self.app_package)
                await asyncio.sleep(3)

                # Verify we're on main page
                current_page = await self._detect_current_page()
                if current_page == "main_page":
                    console.print("[green]✅ Successfully returned to main page via app restart[/green]")
                    self.current_page = "main_page"
                    self.is_in_main_app = True
                    return True

            console.print("[red]❌ Failed to navigate to main page[/red]")
            return False

        except Exception as e:
            logger.error(f"Failed to navigate to main page: {e}")
            return False

    async def _step_ensure_main_page(self) -> Dict[str, Any]:
        """Ensure we are on the main page with app awareness"""
        try:
            console.print("[cyan]🏠 Ensuring we are on the main page...[/cyan]")

            # First detect current app
            app_info = await self._detect_current_app()

            # Then detect current page
            current_page = await self._detect_current_page()
            self.current_page = current_page

            # Check if we're already in the right place
            if app_info["is_main_app"] and current_page == "main_page":
                console.print("[green]✅ Already on main page in main app[/green]")
                return {"success": True}

            # Log current state for debugging
            console.print(f"[cyan]📱 Current app: {app_info['package']}[/cyan]")
            console.print(f"[cyan]🏠 Main app: {self.app_package}[/cyan]")
            console.print(f"[cyan]📍 Current page: {current_page}[/cyan]")
            console.print(f"[cyan]🌐 In external app: {not app_info['is_main_app']}[/cyan]")

            # Navigate to main page (this handles both app switching and page navigation)
            navigation_success = await self._navigate_to_main_page()
            if navigation_success:
                return {"success": True}
            else:
                return {"success": False, "error": "Failed to navigate to main page"}

        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _step_launch_app_enhanced(self) -> Dict[str, Any]:
        """Enhanced app launch with state tracking"""
        try:
            console.print("[cyan]🚀 Launching application...[/cyan]")

            # Get current app and store package name
            if self.device:
                current_app = self.device.app_current()
                if current_app and current_app.get("package"):
                    self.app_package = current_app.get("package")
                    self.current_app_package = self.app_package
                    self.is_in_main_app = True
                    console.print(f"[green]✅ Application already running: {self.app_package}[/green]")

                    # Detect current page
                    self.current_page = await self._detect_current_page()

                    # Log app state
                    console.print(f"[cyan]📱 Main app package set to: {self.app_package}[/cyan]")
                    console.print(f"[cyan]📍 Current page detected as: {self.current_page}[/cyan]")

                    return {"success": True}

            console.print("[green]✅ Application launch step completed[/green]")
            return {"success": True}

        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _update_navigation_state(self, element_clicked: str):
        """Update navigation state based on clicked element with app awareness"""
        try:
            # Detect current app after click
            app_info = await self._detect_current_app()

            # Add to navigation history
            self.navigation_history.append({
                "from_page": self.current_page,
                "clicked_element": element_clicked,
                "timestamp": datetime.now().isoformat(),
                "app_package": app_info["package"],
                "is_main_app": app_info["is_main_app"]
            })

            # Check if we've navigated to external app
            if not app_info["is_main_app"]:
                console.print(f"[yellow]🌐 Navigation led to external app: {app_info['package']}[/yellow]")
                self.is_in_main_app = False
                self.current_app_package = app_info["package"]

                # Add to external app history
                self.external_app_history.append({
                    "package": app_info["package"],
                    "triggered_by": element_clicked,
                    "from_page": self.current_page,
                    "timestamp": datetime.now().isoformat()
                })

                # Set page to external since we're not in main app
                self.current_page = "external_app"
                console.print(f"[yellow]📍 Now in external app: {app_info['package']}[/yellow]")
                return

            # We're still in main app - update page based on clicked element
            if element_clicked == "Ruang Murid":
                self.current_page = "ruang_murid"
                console.print("[cyan]📍 Navigated to Ruang Murid page[/cyan]")
            elif element_clicked == "Sumber Belajar":
                self.current_page = "sumber_belajar"
                console.print("[cyan]📍 Navigated to Sumber Belajar page[/cyan]")
            else:
                # Try to detect page automatically
                detected_page = await self._detect_current_page()
                if detected_page != "unknown":
                    self.current_page = detected_page
                    console.print(f"[cyan]📍 Auto-detected page: {detected_page}[/cyan]")
                else:
                    # Check if this might be an external link
                    await asyncio.sleep(2)  # Wait a bit for potential app switch
                    app_info_recheck = await self._detect_current_app()
                    if not app_info_recheck["is_main_app"]:
                        console.print(f"[yellow]🌐 Delayed detection: Now in external app: {app_info_recheck['package']}[/yellow]")
                        self.is_in_main_app = False
                        self.current_page = "external_app"

        except Exception as e:
            logger.debug(f"Failed to update navigation state: {e}")

    def _extract_text_from_step(self, step: str) -> str:
        """Extract text to enter from step"""
        import re
        matches = re.findall(r'"([^"]*)"', step)
        if matches:
            return matches[0]
        return "test data"

    async def _highlight_element(self, element):
        """Highlight element with green overlay"""
        try:
            # This would require additional implementation for visual highlighting
            # For now, just log the action
            logger.info(f"Highlighting element: {element}")

        except Exception as e:
            logger.warning(f"Failed to highlight element: {e}")

    async def _take_screenshot(self, name: str) -> Optional[str]:
        """Take screenshot"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"screenshot_{name}_{timestamp}.png"
            filepath = os.path.join(self.config.get("SCREENSHOTS_PATH"), filename)

            self.device.screenshot(filepath)

            return filepath

        except Exception as e:
            logger.error(f"Failed to take screenshot: {e}")
            return None

    async def _start_recording(self, test_file: str):
        """Start video recording for test file"""
        try:
            if self.config.get("VIDEO_RECORDING", True):  # Enable by default
                self.recording = True
                self.current_test_file = test_file
                logger.info(f"Started recording for {test_file}")

        except Exception as e:
            logger.warning(f"Failed to start recording: {e}")

    async def _start_scenario_recording(self, scenario_name: str) -> str:
        """Start real screen recording for a specific scenario using ADB screenrecord"""
        try:
            if not self.config.get("VIDEO_RECORDING", True):
                return ""

            # Create video filename based on scenario name
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            safe_scenario_name = "".join(c for c in scenario_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
            safe_scenario_name = safe_scenario_name.replace(' ', '_')[:50]  # Limit length

            video_filename = f"{safe_scenario_name}_{timestamp}.mp4"
            video_path = os.path.join(self.config.get("VIDEOS_PATH", "./data/videos"), video_filename)

            # Ensure videos directory exists
            os.makedirs(os.path.dirname(video_path), exist_ok=True)

            # Start actual screen recording using ADB
            success = await self._start_adb_screen_recording(video_path)

            if success:
                self.current_video_path = video_path
                self.recording = True
                console.print(f"[cyan]🎥 Started recording scenario: {video_filename}[/cyan]")
                logger.info(f"Started scenario recording: {video_path}")
                return video_path
            else:
                console.print(f"[red]❌ Failed to start screen recording for scenario: {scenario_name}[/red]")
                return ""

        except Exception as e:
            logger.warning(f"Failed to start scenario recording: {e}")
            return ""

    async def _start_adb_screen_recording(self, video_path: str) -> bool:
        """Start ADB screen recording to capture emulator screen"""
        try:
            # Get device serial from android manager
            device_serial = None
            if hasattr(self, 'android_manager') and self.android_manager:
                devices = await self.android_manager.get_connected_devices()
                if devices:
                    # Handle both dict and string formats
                    if isinstance(devices[0], dict):
                        device_serial = devices[0].get("serial")
                    else:
                        device_serial = devices[0]

            # Fallback: get device serial directly from ADB
            if not device_serial:
                try:
                    result = subprocess.run(["adb", "devices"], capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        lines = result.stdout.strip().split('\n')[1:]  # Skip header
                        devices = [line.split()[0] for line in lines if line.strip() and 'device' in line]
                        if devices:
                            device_serial = devices[0]
                            console.print(f"[cyan]📱 Using device serial from ADB: {device_serial}[/cyan]")
                except Exception as e:
                    logger.debug(f"Failed to get device serial from ADB: {e}")

            if not device_serial:
                console.print("[red]❌ No device serial found for screen recording[/red]")
                return False

            # Remote path on device for temporary video
            remote_video_path = f"/sdcard/scenario_recording_{int(time.time())}.mp4"

            # Start ADB screenrecord command
            cmd = [
                "adb", "-s", device_serial, "shell", "screenrecord",
                "--time-limit", "180",  # 3 minutes max
                "--bit-rate", "4000000",  # 4Mbps for good quality
                remote_video_path
            ]

            console.print(f"[cyan]🎬 Starting ADB screen recording: {remote_video_path}[/cyan]")

            # Start recording process in background
            self.current_recording_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                stdin=subprocess.PIPE
            )

            # Store paths for later retrieval
            self.current_video_path = video_path
            self.remote_video_path = remote_video_path
            self.device_serial = device_serial

            # Wait a moment for recording to start
            await asyncio.sleep(2)

            console.print(f"[green]✅ Screen recording started successfully[/green]")
            return True

        except Exception as e:
            logger.error(f"Failed to start ADB screen recording: {e}")
            console.print(f"[red]❌ Failed to start screen recording: {e}[/red]")
            return False

    async def _stop_scenario_recording(self) -> str:
        """Stop ADB screen recording and pull video file from device"""
        try:
            if not self.recording or not self.current_recording_process:
                return ""

            console.print("[cyan]🛑 Stopping screen recording...[/cyan]")

            # Stop the recording process
            self.current_recording_process.terminate()

            # Wait for process to finish
            try:
                self.current_recording_process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                self.current_recording_process.kill()
                console.print("[yellow]⚠️ Recording process killed due to timeout[/yellow]")

            # Wait a moment for file to be written
            await asyncio.sleep(3)

            # Pull the video file from device to local path
            success = await self._pull_video_from_device()

            if success and self.current_video_path:
                video_path = self.current_video_path
                self.recording = False

                console.print(f"[cyan]🎥 Recording saved: {os.path.basename(video_path)}[/cyan]")
                logger.info(f"Stopped scenario recording: {video_path}")

                # Clean up
                await self._cleanup_device_video()

                return video_path
            else:
                console.print("[red]❌ Failed to retrieve video from device[/red]")
                return ""

        except Exception as e:
            logger.warning(f"Failed to stop scenario recording: {e}")
            console.print(f"[red]❌ Error stopping recording: {e}[/red]")
            return ""

    async def _pull_video_from_device(self) -> bool:
        """Pull video file from device to local storage"""
        try:
            if not hasattr(self, 'remote_video_path') or not hasattr(self, 'device_serial'):
                return False

            # Use ADB to pull the video file
            cmd = [
                "adb", "-s", self.device_serial, "pull",
                self.remote_video_path,
                self.current_video_path
            ]

            console.print(f"[cyan]📥 Pulling video from device...[/cyan]")

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                console.print(f"[green]✅ Video pulled successfully[/green]")
                return True
            else:
                console.print(f"[red]❌ Failed to pull video: {result.stderr}[/red]")
                return False

        except Exception as e:
            logger.error(f"Failed to pull video from device: {e}")
            return False

    async def _cleanup_device_video(self):
        """Remove temporary video file from device"""
        try:
            if hasattr(self, 'remote_video_path') and hasattr(self, 'device_serial'):
                cmd = [
                    "adb", "-s", self.device_serial, "shell", "rm",
                    self.remote_video_path
                ]

                subprocess.run(cmd, capture_output=True, timeout=10)
                console.print(f"[cyan]🧹 Cleaned up device video file[/cyan]")

        except Exception as e:
            logger.debug(f"Failed to cleanup device video: {e}")

    async def _wait_for_recording_active(self):
        """Wait for screen recording to become active"""
        try:
            console.print("[cyan]⏳ Waiting for screen recording to activate...[/cyan]")
            await asyncio.sleep(3)  # Wait for recording to start properly
            console.print("[green]✅ Screen recording is now active[/green]")
        except Exception as e:
            logger.debug(f"Error waiting for recording: {e}")

    async def _wait_for_page_loading_complete(self):
        """Wait for page loading to complete before stopping recording"""
        try:
            console.print("[cyan]📄 Checking for page loading completion...[/cyan]")

            # Strategy 1: Wait for loading indicators to disappear
            await self._wait_for_loading_indicators_to_disappear()

            # Strategy 2: Wait for page stability (no UI changes)
            await self._wait_for_ui_stability()

            # Strategy 3: Additional wait for slow loading elements
            await self._wait_for_slow_loading_elements()

            console.print("[green]✅ Page loading completed[/green]")

        except Exception as e:
            logger.debug(f"Error waiting for page loading: {e}")
            # Fallback: minimum wait time
            console.print("[yellow]⚠️ Using fallback wait time for page loading[/yellow]")
            await asyncio.sleep(3)

    async def _wait_for_loading_indicators_to_disappear(self):
        """Wait for common loading indicators to disappear"""
        try:
            loading_indicators = [
                "Loading...",
                "Please wait...",
                "Memuat...",
                "Tunggu...",
                "Loading",
                "Sedang memuat",
                "Mohon tunggu"
            ]

            max_wait_time = 10  # Maximum 10 seconds
            check_interval = 0.5  # Check every 500ms
            elapsed_time = 0

            while elapsed_time < max_wait_time:
                loading_found = False

                # Check for loading text indicators
                for indicator in loading_indicators:
                    try:
                        if self.device and hasattr(self.device, '__call__'):
                            if self.device(textContains=indicator).exists:
                                loading_found = True
                                console.print(f"[cyan]⏳ Loading indicator found: '{indicator}'[/cyan]")
                                break
                    except:
                        continue

                if not loading_found:
                    console.print("[green]✅ No loading indicators found[/green]")
                    break

                await asyncio.sleep(check_interval)
                elapsed_time += check_interval

        except Exception as e:
            logger.debug(f"Error checking loading indicators: {e}")

    async def _wait_for_ui_stability(self):
        """Wait for UI to become stable (no changes for a period)"""
        try:
            console.print("[cyan]🔄 Waiting for UI stability...[/cyan]")

            stability_duration = 2.0  # UI must be stable for 2 seconds
            check_interval = 0.5  # Check every 500ms
            max_wait_time = 8  # Maximum 8 seconds total

            stable_start_time = None
            elapsed_time = 0

            while elapsed_time < max_wait_time:
                # Take a screenshot to check for changes
                try:
                    if self.device and hasattr(self.device, 'screenshot'):
                        current_screenshot = self.device.screenshot()

                        # Compare with previous screenshot if available
                        if hasattr(self, '_previous_screenshot'):
                            # Simple comparison - in real implementation, you might want
                            # more sophisticated image comparison
                            if current_screenshot.size == self._previous_screenshot.size:
                                if stable_start_time is None:
                                    stable_start_time = asyncio.get_event_loop().time()
                                elif (asyncio.get_event_loop().time() - stable_start_time) >= stability_duration:
                                    console.print("[green]✅ UI is stable[/green]")
                                    break
                            else:
                                stable_start_time = None

                        self._previous_screenshot = current_screenshot

                except Exception:
                    # If screenshot fails, just wait a bit
                    pass

                await asyncio.sleep(check_interval)
                elapsed_time += check_interval

            # Clean up
            if hasattr(self, '_previous_screenshot'):
                delattr(self, '_previous_screenshot')

        except Exception as e:
            logger.debug(f"Error checking UI stability: {e}")

    async def _wait_for_slow_loading_elements(self):
        """Wait for elements that typically load slowly"""
        try:
            console.print("[cyan]🖼️ Waiting for slow loading elements...[/cyan]")

            # Common slow-loading elements
            slow_elements = [
                "image",  # Images
                "video",  # Videos
                "webview",  # WebViews
                "recyclerview",  # Lists that might be loading data
                "progressbar"  # Progress bars
            ]

            # Wait a bit for these elements to finish loading
            await asyncio.sleep(2)

            # Check if any progress bars are still active
            try:
                if self.device and hasattr(self.device, '__call__'):
                    # Look for progress bars or loading spinners
                    if self.device(className="android.widget.ProgressBar").exists:
                        console.print("[cyan]⏳ Progress bar detected, waiting...[/cyan]")
                        await asyncio.sleep(2)
            except:
                pass

            console.print("[green]✅ Slow loading elements check completed[/green]")

        except Exception as e:
            logger.debug(f"Error checking slow loading elements: {e}")

    async def _add_visual_highlight(self, element=None, action_type="click", element_text=""):
        """Add simple, reliable visual highlight to show AI action on screen"""
        try:
            if not self.visual_feedback_enabled or not self.highlight_enabled:
                # Just show console feedback
                console.print(f"[cyan]🔴 AI ACTION: {action_type.upper()}[/cyan]")
                if element_text:
                    console.print(f"[cyan]🎯 Target: '{element_text}'[/cyan]")
                return

            console.print(f"[red]🔴 AI ACTION: {action_type.upper()}[/red]")

            # Show what element is being highlighted
            if element_text:
                console.print(f"[red]🎯 Highlighting element: '{element_text}'[/red]")

            # Get device serial for ADB commands
            device_serial = await self._get_device_serial()
            if not device_serial:
                console.print("[yellow]⚠️ No device serial found, using console feedback only[/yellow]")
                return

            # Use simple, reliable highlighting only
            if self.simple_highlighting_only:
                await self._create_simple_visual_highlight(element, action_type, element_text, device_serial)
            else:
                # Fallback to console only if complex highlighting is disabled
                console.print(f"[cyan]✅ Action target identified: '{element_text}'[/cyan]")

        except Exception as e:
            console.print(f"[yellow]⚠️ Visual highlight failed (continuing test): {e}[/yellow]")
            logger.debug(f"Visual highlight error: {e}")

    async def _create_simple_visual_highlight(self, element, action_type, element_text, device_serial):
        """Create simple, reliable visual highlighting without interfering with tests"""
        try:
            console.print(f"[cyan]🎯 Creating simple visual highlight for {action_type}[/cyan]")

            # Method 1: Enable show touches (most reliable and non-interfering)
            try:
                await self._enable_show_touches(device_serial)
            except Exception as e:
                logger.debug(f"Show touches failed: {e}")

            # Method 2: Simple console notification (always works)
            if element_text:
                console.print(f"[green]✅ Target element: '{element_text}'[/green]")

            # Method 3: Brief visual indicator (optional, safe) - DISABLED to prevent interference
            # This was causing issues with test execution
            # if element and hasattr(element, 'info'):
            #     await self._create_safe_element_indicator(element, action_type, device_serial)

            # Brief pause to show the highlight without interfering
            await asyncio.sleep(0.2)  # Reduced from 0.5 to minimize delay

        except Exception as e:
            console.print(f"[yellow]⚠️ Simple highlight failed (continuing): {e}[/yellow]")
            logger.debug(f"Simple highlight error: {e}")

    async def _create_safe_element_indicator(self, element, action_type, device_serial):
        """Create a safe, non-interfering element indicator"""
        try:
            # Get element bounds safely
            element_info = element.info
            bounds = element_info.get('bounds', {})

            if not bounds:
                return

            # Parse bounds safely
            if isinstance(bounds, dict):
                x1 = bounds.get('left', 0)
                y1 = bounds.get('top', 0)
                x2 = bounds.get('right', 100)
                y2 = bounds.get('bottom', 100)
            elif isinstance(bounds, (list, tuple)) and len(bounds) >= 4:
                x1, y1, x2, y2 = bounds[:4]
            else:
                return

            # Simple corner taps only (minimal interference)
            corner_points = [(x1, y1), (x2, y1), (x1, y2), (x2, y2)]

            for x, y in corner_points:
                subprocess.run([
                    "adb", "-s", device_serial, "shell", "input", "tap", str(int(x)), str(int(y))
                ], capture_output=True, timeout=1)
                await asyncio.sleep(0.05)  # Very brief pause

            console.print(f"[cyan]📍 Element bounds: ({x1},{y1}) to ({x2},{y2})[/cyan]")

        except Exception as e:
            logger.debug(f"Safe element indicator error: {e}")

    def disable_visual_highlighting(self):
        """Disable all visual highlighting to prevent interference with tests"""
        self.visual_feedback_enabled = False
        self.highlight_enabled = False
        console.print("[yellow]⚠️ Visual highlighting disabled - using console feedback only[/yellow]")

    def enable_simple_highlighting(self):
        """Enable only simple, safe visual highlighting"""
        self.visual_feedback_enabled = True
        self.highlight_enabled = True
        self.simple_highlighting_only = True
        console.print("[green]✅ Simple visual highlighting enabled[/green]")

    async def _enable_show_touches(self, device_serial: str):
        """Enable show touches - most reliable visual feedback"""
        try:
            console.print("[red]👆 Enabling show touches for visual feedback[/red]")

            # Enable show touches in developer options
            result = subprocess.run([
                "adb", "-s", device_serial, "shell",
                "settings", "put", "system", "show_touches", "1"
            ], capture_output=True, text=True, timeout=3)

            if result.returncode == 0:
                console.print("[green]✅ Show touches enabled successfully[/green]")
            else:
                console.print(f"[yellow]⚠️ Show touches enable failed: {result.stderr}[/yellow]")

        except Exception as e:
            console.print(f"[red]❌ Error enabling show touches: {e}[/red]")
            logger.debug(f"Error enabling show touches: {e}")

    async def _highlight_element_with_border_taps(self, element, device_serial):
        """Create red border around element using rapid taps"""
        try:
            # Get element bounds
            element_info = element.info
            bounds = element_info.get('bounds', {})

            if not bounds:
                console.print("[yellow]⚠️ No element bounds found for border highlighting[/yellow]")
                return

            # Parse bounds - format is typically [x1, y1, x2, y2]
            if isinstance(bounds, dict):
                x1 = bounds.get('left', 0)
                y1 = bounds.get('top', 0)
                x2 = bounds.get('right', 100)
                y2 = bounds.get('bottom', 100)
            elif isinstance(bounds, (list, tuple)) and len(bounds) >= 4:
                x1, y1, x2, y2 = bounds[:4]
            else:
                console.print(f"[yellow]⚠️ Unexpected bounds format: {bounds}[/yellow]")
                return

            console.print(f"[red]📍 Creating border taps around element: ({x1},{y1}) to ({x2},{y2})[/red]")

            # Create border with rapid taps (this will be visible with show_touches enabled)
            border_points = [
                # Top border (5 points)
                (x1, y1), (x1 + (x2-x1)//4, y1), (x1 + (x2-x1)//2, y1), (x1 + 3*(x2-x1)//4, y1), (x2, y1),
                # Right border (3 points)
                (x2, y1 + (y2-y1)//3), (x2, y1 + 2*(y2-y1)//3), (x2, y2),
                # Bottom border (4 points)
                (x1 + 3*(x2-x1)//4, y2), (x1 + (x2-x1)//2, y2), (x1 + (x2-x1)//4, y2), (x1, y2),
                # Left border (2 points)
                (x1, y1 + 2*(y2-y1)//3), (x1, y1 + (y2-y1)//3)
            ]

            # Rapidly tap border points to create visible red border
            for x, y in border_points:
                subprocess.run([
                    "adb", "-s", device_serial, "shell", "input", "tap", str(int(x)), str(int(y))
                ], capture_output=True, timeout=1)
                await asyncio.sleep(0.03)  # Very quick taps for border effect

            console.print("[red]⭕ Created red border around element[/red]")

        except Exception as e:
            console.print(f"[red]❌ Error creating border taps: {e}[/red]")
            logger.debug(f"Error creating border taps: {e}")

    async def _create_visible_notification(self, action_type, element_text, device_serial):
        """Create visible notification for the action"""
        try:
            # Create notification message
            if element_text:
                short_text = element_text[:15] + "..." if len(element_text) > 15 else element_text
                message = f"🔴 AI {action_type}: {short_text}"
            else:
                message = f"🔴 AI {action_type}"

            console.print(f"[red]📢 Creating notification: {message}[/red]")

            # Method 1: Use notification command
            subprocess.run([
                "adb", "-s", device_serial, "shell",
                "cmd", "notification", "post", "-t", message,
                "ai_action", f"AI performing {action_type}"
            ], capture_output=True, timeout=3)

            # Method 2: Create toast using am broadcast
            subprocess.run([
                "adb", "-s", device_serial, "shell",
                "am", "broadcast", "-a", "android.intent.action.SHOW_APP_INFO",
                "--es", "message", message
            ], capture_output=True, timeout=3)

        except Exception as e:
            console.print(f"[red]❌ Error creating notification: {e}[/red]")
            logger.debug(f"Error creating notification: {e}")

    async def _create_simple_pulse_effect(self, element, device_serial):
        """Create simple pulse effect at element center"""
        try:
            if not element or not hasattr(element, 'info'):
                return

            element_info = element.info
            bounds = element_info.get('bounds', {})

            if not bounds:
                return

            # Calculate center point
            if isinstance(bounds, dict):
                center_x = (bounds.get('left', 0) + bounds.get('right', 100)) // 2
                center_y = (bounds.get('top', 0) + bounds.get('bottom', 100)) // 2
            elif isinstance(bounds, (list, tuple)) and len(bounds) >= 4:
                center_x = (bounds[0] + bounds[2]) // 2
                center_y = (bounds[1] + bounds[3]) // 2
            else:
                return

            console.print(f"[red]💓 Creating pulse effect at ({center_x}, {center_y})[/red]")

            # Create 3 pulses with expanding circles
            for pulse in range(3):
                for radius in [5, 10, 15]:
                    # Tap at 4 points around the center
                    for angle in [0, 90, 180, 270]:
                        import math
                        x = center_x + int(radius * math.cos(math.radians(angle)))
                        y = center_y + int(radius * math.sin(math.radians(angle)))

                        subprocess.run([
                            "adb", "-s", device_serial, "shell", "input", "tap", str(x), str(y)
                        ], capture_output=True, timeout=1)

                await asyncio.sleep(0.1)  # Pause between pulses

            console.print("[red]✨ Pulse effect completed[/red]")

        except Exception as e:
            console.print(f"[red]❌ Error creating pulse effect: {e}[/red]")
            logger.debug(f"Error creating pulse effect: {e}")

    async def _create_enhanced_red_border_highlight(self, element, action_type, device_serial):
        """Create enhanced red border highlighting - most visible method"""
        try:
            console.print(f"[red]🔴 Creating ENHANCED red border highlight for {action_type}[/red]")

            # Get FRESH element bounds (adapts to current position)
            fresh_bounds = await self._get_fresh_element_bounds(element)

            if not fresh_bounds:
                console.print("[yellow]⚠️ No element bounds found for enhanced highlight[/yellow]")
                return

            x1, y1, x2, y2 = fresh_bounds
            console.print(f"[red]📍 Enhanced highlighting at: ({x1},{y1}) to ({x2},{y2})[/red]")

            # Create THICK red border using multiple methods for maximum visibility
            await self._create_ultra_thick_red_border(device_serial, x1, y1, x2, y2)

            # Add pulsing effect for extra visibility
            await self._create_enhanced_pulsing_effect(device_serial, x1, y1, x2, y2)

            # Color coding like web automation with enhanced visibility
            if action_type == "CLICK":
                await self._create_enhanced_click_highlight(device_serial, x1, y1, x2, y2)
            elif action_type == "INPUT":
                await self._create_enhanced_input_highlight(device_serial, x1, y1, x2, y2)
            elif action_type == "ASSERT":
                await self._create_enhanced_assert_highlight(device_serial, x1, y1, x2, y2)

        except Exception as e:
            console.print(f"[red]❌ Error creating enhanced highlight: {e}[/red]")
            logger.debug(f"Error creating enhanced highlight: {e}")

    async def _create_multiple_border_tap_sequences(self, element, device_serial):
        """Create multiple border tap sequences for maximum visibility"""
        try:
            console.print(f"[red]🎯 Creating multiple border tap sequences[/red]")

            fresh_bounds = await self._get_fresh_element_bounds(element)
            if not fresh_bounds:
                return

            x1, y1, x2, y2 = fresh_bounds

            # Create 3 different border tap sequences for maximum visibility
            for sequence in range(3):
                console.print(f"[red]🔄 Border sequence {sequence + 1}/3[/red]")

                # Sequence 1: Dense border taps
                if sequence == 0:
                    await self._create_dense_border_taps(device_serial, x1, y1, x2, y2)
                # Sequence 2: Corner emphasis taps
                elif sequence == 1:
                    await self._create_corner_emphasis_taps(device_serial, x1, y1, x2, y2)
                # Sequence 3: Expanding border taps
                else:
                    await self._create_expanding_border_taps(device_serial, x1, y1, x2, y2)

                await asyncio.sleep(0.2)  # Brief pause between sequences

        except Exception as e:
            console.print(f"[red]❌ Error creating border sequences: {e}[/red]")
            logger.debug(f"Error creating border sequences: {e}")

    async def _create_fallback_visual_indicator(self, action_type, device_serial):
        """Create fallback visual indicator when element bounds are not available"""
        try:
            console.print(f"[red]🚨 Creating fallback visual indicator for {action_type}[/red]")

            # Get screen center for fallback indication
            screen_center_x, screen_center_y = await self._get_screen_center(device_serial)

            # Create large visible indicator at screen center
            await self._create_large_center_indicator(device_serial, screen_center_x, screen_center_y, action_type)

            # Create screen-wide flash effect
            await self._create_enhanced_screen_flash(device_serial, action_type)

        except Exception as e:
            console.print(f"[red]❌ Error creating fallback indicator: {e}[/red]")
            logger.debug(f"Error creating fallback indicator: {e}")

    async def _create_android_visual_overlay(self, element, action_type, device_serial):
        """Create Android visual overlay - mobile equivalent of web CSS styling"""
        try:
            console.print(f"[red]🎯 Creating Android visual overlay for {action_type}[/red]")

            # Get FRESH element bounds (adapts to current position)
            fresh_bounds = await self._get_fresh_element_bounds(element)

            if not fresh_bounds:
                console.print("[yellow]⚠️ No element bounds found for overlay[/yellow]")
                return

            x1, y1, x2, y2 = fresh_bounds
            console.print(f"[cyan]📍 Fresh element bounds: ({x1},{y1}) to ({x2},{y2})[/cyan]")

            # Color coding like web automation
            if action_type == "CLICK":
                await self._create_adaptive_red_border_overlay(element, device_serial)
            elif action_type == "INPUT":
                await self._create_adaptive_yellow_background_overlay(element, device_serial)
            elif action_type == "ASSERT":
                await self._create_adaptive_green_highlight_overlay(element, device_serial)

        except Exception as e:
            console.print(f"[red]❌ Error creating Android overlay: {e}[/red]")
            logger.debug(f"Error creating Android overlay: {e}")

    async def _get_fresh_element_bounds(self, element):
        """Get fresh element bounds that adapt to current emulator position"""
        try:
            # Refresh element to get current position
            if hasattr(element, 'info'):
                # Force refresh element info
                element_info = element.info
                bounds = element_info.get('bounds', {})

                if isinstance(bounds, dict):
                    x1 = bounds.get('left', 0)
                    y1 = bounds.get('top', 0)
                    x2 = bounds.get('right', 100)
                    y2 = bounds.get('bottom', 100)
                    return (x1, y1, x2, y2)
                elif isinstance(bounds, (list, tuple)) and len(bounds) >= 4:
                    return bounds[:4]

            return None

        except Exception as e:
            logger.debug(f"Error getting fresh element bounds: {e}")
            return None

    async def _create_adaptive_red_border_overlay(self, element, device_serial):
        try:
            # Use Android's accessibility overlay to draw red border
            subprocess.run([
                "adb", "-s", device_serial, "shell",
                "settings", "put", "system", "enable_accessibility_overlay", "1"
            ])
            
            # Get element bounds
            bounds = await self._get_fresh_element_bounds(element)
            if not bounds:
                return
                
            # Create overlay using Android's WindowManager
            overlay_cmd = f"""
            am broadcast -a com.android.systemui.demo -e command overlay \
            -e rect {bounds[0]},{bounds[1]},{bounds[2]},{bounds[3]} \
            -e color #FF0000 -e width 5
            """
            subprocess.run(["adb", "-s", device_serial, "shell", overlay_cmd])
            
            # Remove overlay after delay
            await asyncio.sleep(1.5)
            subprocess.run(["adb", "-s", device_serial, "shell", "am broadcast -a com.android.systemui.demo -e command overlay -e clear true"])
        except Exception as e:
            logger.debug(f"Error creating red border overlay: {e}")

    async def _create_adaptive_yellow_background_overlay(self, element, device_serial):
        """Create adaptive yellow background that follows element position"""
        try:
            console.print(f"[yellow]🟡 Creating adaptive yellow background overlay[/yellow]")

            # Get fresh bounds right before drawing
            bounds = await self._get_fresh_element_bounds(element)
            if not bounds:
                return

            x1, y1, x2, y2 = bounds
            console.print(f"[yellow]📍 Drawing yellow background at current position: ({x1},{y1}) to ({x2},{y2})[/yellow]")

            # Fill area with yellow taps (simulating background)
            for y in range(y1 + 2, y2 - 2, 8):
                for x in range(x1 + 2, x2 - 2, 8):
                    subprocess.run([
                        "adb", "-s", device_serial, "shell", "input", "tap",
                        str(x), str(y)
                    ], capture_output=True, timeout=1)

            # Add red border around yellow background
            await self._create_adaptive_red_border_overlay(element, device_serial)

            console.print("[yellow]🟡 Adaptive yellow background overlay created[/yellow]")

        except Exception as e:
            logger.debug(f"Error creating adaptive yellow background overlay: {e}")

    async def _create_adaptive_green_highlight_overlay(self, element, device_serial):
        """Create adaptive green highlight that follows element position"""
        try:
            console.print(f"[green]🟢 Creating adaptive green highlight overlay[/green]")

            # Get fresh bounds right before drawing
            bounds = await self._get_fresh_element_bounds(element)
            if not bounds:
                return

            x1, y1, x2, y2 = bounds
            console.print(f"[green]📍 Drawing green highlight at current position: ({x1},{y1}) to ({x2},{y2})[/green]")

            # Create green border
            border_thickness = 2

            for thickness in range(border_thickness):
                # Top and bottom borders
                for x in range(x1, x2, 4):
                    subprocess.run([
                        "adb", "-s", device_serial, "shell", "input", "tap",
                        str(x), str(y1 + thickness)
                    ], capture_output=True, timeout=1)
                    subprocess.run([
                        "adb", "-s", device_serial, "shell", "input", "tap",
                        str(x), str(y2 - thickness)
                    ], capture_output=True, timeout=1)

                # Left and right borders
                for y in range(y1, y2, 4):
                    subprocess.run([
                        "adb", "-s", device_serial, "shell", "input", "tap",
                        str(x1 + thickness), str(y)
                    ], capture_output=True, timeout=1)
                    subprocess.run([
                        "adb", "-s", device_serial, "shell", "input", "tap",
                        str(x2 - thickness), str(y)
                    ], capture_output=True, timeout=1)

            console.print("[green]🟢 Adaptive green highlight overlay created[/green]")

        except Exception as e:
            logger.debug(f"Error creating adaptive green highlight overlay: {e}")

    async def _create_red_border_overlay(self, device_serial, x1, y1, x2, y2):
        """Create red border overlay - equivalent to CSS red border"""
        try:
            console.print(f"[red]🔴 Creating red border overlay: ({x1},{y1}) to ({x2},{y2})[/red]")

            # Create thick red border using multiple tap lines
            border_thickness = 3

            for thickness in range(border_thickness):
                # Top border
                for x in range(x1, x2, 5):
                    subprocess.run([
                        "adb", "-s", device_serial, "shell", "input", "tap",
                        str(x), str(y1 + thickness)
                    ], capture_output=True, timeout=1)

                # Bottom border
                for x in range(x1, x2, 5):
                    subprocess.run([
                        "adb", "-s", device_serial, "shell", "input", "tap",
                        str(x), str(y2 - thickness)
                    ], capture_output=True, timeout=1)

                # Left border
                for y in range(y1, y2, 5):
                    subprocess.run([
                        "adb", "-s", device_serial, "shell", "input", "tap",
                        str(x1 + thickness), str(y)
                    ], capture_output=True, timeout=1)

                # Right border
                for y in range(y1, y2, 5):
                    subprocess.run([
                        "adb", "-s", device_serial, "shell", "input", "tap",
                        str(x2 - thickness), str(y)
                    ], capture_output=True, timeout=1)

            console.print("[red]🔴 Red border overlay created[/red]")

        except Exception as e:
            logger.debug(f"Error creating red border overlay: {e}")

    async def _create_yellow_background_overlay(self, device_serial, x1, y1, x2, y2):
        """Create yellow background overlay for input fields - equivalent to CSS yellow background"""
        try:
            console.print(f"[yellow]🟡 Creating yellow background overlay: ({x1},{y1}) to ({x2},{y2})[/yellow]")

            # Fill area with yellow taps (simulating background)
            for y in range(y1 + 2, y2 - 2, 8):
                for x in range(x1 + 2, x2 - 2, 8):
                    subprocess.run([
                        "adb", "-s", device_serial, "shell", "input", "tap",
                        str(x), str(y)
                    ], capture_output=True, timeout=1)

            # Add red border around yellow background
            await self._create_red_border_overlay(device_serial, x1, y1, x2, y2)

            console.print("[yellow]🟡 Yellow background overlay created[/yellow]")

        except Exception as e:
            logger.debug(f"Error creating yellow background overlay: {e}")

    async def _create_ultra_thick_red_border(self, device_serial, x1, y1, x2, y2):
        """Create ultra-thick red border for maximum visibility"""
        try:
            console.print(f"[red]🔴 Creating ULTRA-THICK red border: ({x1},{y1}) to ({x2},{y2})[/red]")

            # Create VERY thick border (8 pixels thick) for maximum visibility
            border_thickness = 8

            for thickness in range(border_thickness):
                # Top border - very dense taps
                for x in range(x1, x2, 2):  # Every 2 pixels for density
                    subprocess.run([
                        "adb", "-s", device_serial, "shell", "input", "tap",
                        str(x), str(y1 + thickness)
                    ], capture_output=True, timeout=1)

                # Bottom border - very dense taps
                for x in range(x1, x2, 2):
                    subprocess.run([
                        "adb", "-s", device_serial, "shell", "input", "tap",
                        str(x), str(y2 - thickness)
                    ], capture_output=True, timeout=1)

                # Left border - very dense taps
                for y in range(y1, y2, 2):
                    subprocess.run([
                        "adb", "-s", device_serial, "shell", "input", "tap",
                        str(x1 + thickness), str(y)
                    ], capture_output=True, timeout=1)

                # Right border - very dense taps
                for y in range(y1, y2, 2):
                    subprocess.run([
                        "adb", "-s", device_serial, "shell", "input", "tap",
                        str(x2 - thickness), str(y)
                    ], capture_output=True, timeout=1)

            console.print("[red]🔴 Ultra-thick red border created[/red]")

        except Exception as e:
            logger.debug(f"Error creating ultra-thick red border: {e}")

    async def _create_enhanced_pulsing_effect(self, device_serial, x1, y1, x2, y2):
        """Create enhanced pulsing effect for maximum visibility"""
        try:
            console.print(f"[red]💓 Creating enhanced pulsing effect[/red]")

            center_x = (x1 + x2) // 2
            center_y = (y1 + y2) // 2

            # Create 5 pulses with expanding circles for maximum visibility
            for pulse in range(5):
                console.print(f"[red]💓 Pulse {pulse + 1}/5[/red]")

                # Create expanding circles with dense taps
                for radius in [10, 20, 30, 40, 50]:
                    # Create full circle with many points for visibility
                    for angle in range(0, 360, 15):  # Every 15 degrees
                        import math
                        x = center_x + int(radius * math.cos(math.radians(angle)))
                        y = center_y + int(radius * math.sin(math.radians(angle)))

                        # Ensure coordinates are within screen bounds
                        if x1 <= x <= x2 and y1 <= y <= y2:
                            subprocess.run([
                                "adb", "-s", device_serial, "shell", "input", "tap", str(x), str(y)
                            ], capture_output=True, timeout=1)

                await asyncio.sleep(0.15)  # Pause between pulses

            console.print("[red]💓 Enhanced pulsing effect completed[/red]")

        except Exception as e:
            logger.debug(f"Error creating enhanced pulsing effect: {e}")

    async def _create_enhanced_click_highlight(self, device_serial, x1, y1, x2, y2):
        """Create enhanced click highlight - red border with glow effect"""
        try:
            console.print(f"[red]🔴 Creating enhanced CLICK highlight[/red]")

            # Create red border with "glow" effect (multiple border layers)
            for glow_layer in range(3):
                offset = glow_layer * 2

                # Outer glow layers
                glow_x1 = max(0, x1 - offset)
                glow_y1 = max(0, y1 - offset)
                glow_x2 = x2 + offset
                glow_y2 = y2 + offset

                # Create glow border
                for x in range(glow_x1, glow_x2, 3):
                    subprocess.run(["adb", "-s", device_serial, "shell", "input", "tap", str(x), str(glow_y1)], capture_output=True, timeout=1)
                    subprocess.run(["adb", "-s", device_serial, "shell", "input", "tap", str(x), str(glow_y2)], capture_output=True, timeout=1)

                for y in range(glow_y1, glow_y2, 3):
                    subprocess.run(["adb", "-s", device_serial, "shell", "input", "tap", str(glow_x1), str(y)], capture_output=True, timeout=1)
                    subprocess.run(["adb", "-s", device_serial, "shell", "input", "tap", str(glow_x2), str(y)], capture_output=True, timeout=1)

            console.print("[red]🔴 Enhanced click highlight created[/red]")

        except Exception as e:
            logger.debug(f"Error creating enhanced click highlight: {e}")

    async def _create_enhanced_input_highlight(self, device_serial, x1, y1, x2, y2):
        """Create enhanced input highlight - yellow background with red border"""
        try:
            console.print(f"[yellow]🟡 Creating enhanced INPUT highlight[/yellow]")

            # Fill area with yellow taps (denser pattern for visibility)
            for y in range(y1 + 1, y2 - 1, 4):
                for x in range(x1 + 1, x2 - 1, 4):
                    subprocess.run([
                        "adb", "-s", device_serial, "shell", "input", "tap", str(x), str(y)
                    ], capture_output=True, timeout=1)

            # Add thick red border around yellow background
            await self._create_ultra_thick_red_border(device_serial, x1, y1, x2, y2)

            console.print("[yellow]🟡 Enhanced input highlight created[/yellow]")

        except Exception as e:
            logger.debug(f"Error creating enhanced input highlight: {e}")

    async def _create_enhanced_assert_highlight(self, device_serial, x1, y1, x2, y2):
        """Create enhanced assert highlight - green border with emphasis"""
        try:
            console.print(f"[green]🟢 Creating enhanced ASSERT highlight[/green]")

            # Create thick green border (similar to ultra-thick red but green concept)
            border_thickness = 6

            for thickness in range(border_thickness):
                # Top and bottom borders - dense taps
                for x in range(x1, x2, 2):
                    subprocess.run([
                        "adb", "-s", device_serial, "shell", "input", "tap",
                        str(x), str(y1 + thickness)
                    ], capture_output=True, timeout=1)
                    subprocess.run([
                        "adb", "-s", device_serial, "shell", "input", "tap",
                        str(x), str(y2 - thickness)
                    ], capture_output=True, timeout=1)

                # Left and right borders - dense taps
                for y in range(y1, y2, 2):
                    subprocess.run([
                        "adb", "-s", device_serial, "shell", "input", "tap",
                        str(x1 + thickness), str(y)
                    ], capture_output=True, timeout=1)
                    subprocess.run([
                        "adb", "-s", device_serial, "shell", "input", "tap",
                        str(x2 - thickness), str(y)
                    ], capture_output=True, timeout=1)

            console.print("[green]🟢 Enhanced assert highlight created[/green]")

        except Exception as e:
            logger.debug(f"Error creating enhanced assert highlight: {e}")

    async def _create_green_highlight_overlay(self, device_serial, x1, y1, x2, y2):
        """Create green highlight overlay for assertions - equivalent to CSS green highlight"""
        try:
            console.print(f"[green]🟢 Creating green highlight overlay: ({x1},{y1}) to ({x2},{y2})[/green]")

            # Create green border
            border_thickness = 2

            for thickness in range(border_thickness):
                # Top and bottom borders
                for x in range(x1, x2, 4):
                    subprocess.run([
                        "adb", "-s", device_serial, "shell", "input", "tap",
                        str(x), str(y1 + thickness)
                    ], capture_output=True, timeout=1)
                    subprocess.run([
                        "adb", "-s", device_serial, "shell", "input", "tap",
                        str(x), str(y2 - thickness)
                    ], capture_output=True, timeout=1)

                # Left and right borders
                for y in range(y1, y2, 4):
                    subprocess.run([
                        "adb", "-s", device_serial, "shell", "input", "tap",
                        str(x1 + thickness), str(y)
                    ], capture_output=True, timeout=1)
                    subprocess.run([
                        "adb", "-s", device_serial, "shell", "input", "tap",
                        str(x2 - thickness), str(y)
                    ], capture_output=True, timeout=1)

            console.print("[green]🟢 Green highlight overlay created[/green]")

        except Exception as e:
            logger.debug(f"Error creating green highlight overlay: {e}")

    async def _create_dense_border_taps(self, device_serial, x1, y1, x2, y2):
        """Create dense border taps for maximum visibility"""
        try:
            console.print(f"[red]🎯 Creating dense border taps[/red]")

            # Very dense taps every 1 pixel for maximum visibility
            # Top border
            for x in range(x1, x2, 1):
                subprocess.run(["adb", "-s", device_serial, "shell", "input", "tap", str(x), str(y1)], capture_output=True, timeout=1)

            # Bottom border
            for x in range(x1, x2, 1):
                subprocess.run(["adb", "-s", device_serial, "shell", "input", "tap", str(x), str(y2)], capture_output=True, timeout=1)

            # Left border
            for y in range(y1, y2, 1):
                subprocess.run(["adb", "-s", device_serial, "shell", "input", "tap", str(x1), str(y)], capture_output=True, timeout=1)

            # Right border
            for y in range(y1, y2, 1):
                subprocess.run(["adb", "-s", device_serial, "shell", "input", "tap", str(x2), str(y)], capture_output=True, timeout=1)

        except Exception as e:
            logger.debug(f"Error creating dense border taps: {e}")

    async def _create_corner_emphasis_taps(self, device_serial, x1, y1, x2, y2):
        """Create corner emphasis taps for visibility"""
        try:
            console.print(f"[red]📐 Creating corner emphasis taps[/red]")

            # Emphasize corners with multiple taps in corner areas
            corner_size = 20

            # Top-left corner
            for x in range(x1, min(x1 + corner_size, x2), 2):
                for y in range(y1, min(y1 + corner_size, y2), 2):
                    subprocess.run(["adb", "-s", device_serial, "shell", "input", "tap", str(x), str(y)], capture_output=True, timeout=1)

            # Top-right corner
            for x in range(max(x2 - corner_size, x1), x2, 2):
                for y in range(y1, min(y1 + corner_size, y2), 2):
                    subprocess.run(["adb", "-s", device_serial, "shell", "input", "tap", str(x), str(y)], capture_output=True, timeout=1)

            # Bottom-left corner
            for x in range(x1, min(x1 + corner_size, x2), 2):
                for y in range(max(y2 - corner_size, y1), y2, 2):
                    subprocess.run(["adb", "-s", device_serial, "shell", "input", "tap", str(x), str(y)], capture_output=True, timeout=1)

            # Bottom-right corner
            for x in range(max(x2 - corner_size, x1), x2, 2):
                for y in range(max(y2 - corner_size, y1), y2, 2):
                    subprocess.run(["adb", "-s", device_serial, "shell", "input", "tap", str(x), str(y)], capture_output=True, timeout=1)

        except Exception as e:
            logger.debug(f"Error creating corner emphasis taps: {e}")

    async def _create_expanding_border_taps(self, device_serial, x1, y1, x2, y2):
        """Create expanding border taps for dynamic visibility"""
        try:
            console.print(f"[red]📈 Creating expanding border taps[/red]")

            # Create expanding border effect
            for expansion in range(5):
                offset = expansion * 3

                # Expand outward
                exp_x1 = max(0, x1 - offset)
                exp_y1 = max(0, y1 - offset)
                exp_x2 = x2 + offset
                exp_y2 = y2 + offset

                # Create border at this expansion level
                for x in range(exp_x1, exp_x2, 5):
                    subprocess.run(["adb", "-s", device_serial, "shell", "input", "tap", str(x), str(exp_y1)], capture_output=True, timeout=1)
                    subprocess.run(["adb", "-s", device_serial, "shell", "input", "tap", str(x), str(exp_y2)], capture_output=True, timeout=1)

                for y in range(exp_y1, exp_y2, 5):
                    subprocess.run(["adb", "-s", device_serial, "shell", "input", "tap", str(exp_x1), str(y)], capture_output=True, timeout=1)
                    subprocess.run(["adb", "-s", device_serial, "shell", "input", "tap", str(exp_x2), str(y)], capture_output=True, timeout=1)

                await asyncio.sleep(0.1)  # Brief pause between expansions

        except Exception as e:
            logger.debug(f"Error creating expanding border taps: {e}")

    async def _create_large_center_indicator(self, device_serial, center_x, center_y, action_type):
        """Create large center indicator for fallback visual feedback"""
        try:
            console.print(f"[red]🎯 Creating large center indicator for {action_type}[/red]")

            # Create large cross pattern at center
            cross_size = 100

            # Horizontal line
            for x in range(center_x - cross_size, center_x + cross_size, 3):
                subprocess.run(["adb", "-s", device_serial, "shell", "input", "tap", str(x), str(center_y)], capture_output=True, timeout=1)

            # Vertical line
            for y in range(center_y - cross_size, center_y + cross_size, 3):
                subprocess.run(["adb", "-s", device_serial, "shell", "input", "tap", str(center_x), str(y)], capture_output=True, timeout=1)

            # Create circle around center
            for radius in [30, 50, 70]:
                for angle in range(0, 360, 10):
                    import math
                    x = center_x + int(radius * math.cos(math.radians(angle)))
                    y = center_y + int(radius * math.sin(math.radians(angle)))
                    subprocess.run(["adb", "-s", device_serial, "shell", "input", "tap", str(x), str(y)], capture_output=True, timeout=1)

        except Exception as e:
            logger.debug(f"Error creating large center indicator: {e}")

    async def _create_enhanced_screen_flash(self, device_serial, action_type):
        """Create enhanced screen flash for maximum visibility"""
        try:
            console.print(f"[red]⚡ Creating enhanced screen flash for {action_type}[/red]")

            # Multiple flash methods for maximum visibility

            # Method 1: Brightness flash
            try:
                # Get current brightness
                result = subprocess.run([
                    "adb", "-s", device_serial, "shell",
                    "settings", "get", "system", "screen_brightness"
                ], capture_output=True, text=True, timeout=3)

                current_brightness = result.stdout.strip() if result.returncode == 0 else "128"

                # Flash to max brightness
                subprocess.run([
                    "adb", "-s", device_serial, "shell",
                    "settings", "put", "system", "screen_brightness", "255"
                ], capture_output=True, timeout=2)

                await asyncio.sleep(0.3)

                # Restore original brightness
                subprocess.run([
                    "adb", "-s", device_serial, "shell",
                    "settings", "put", "system", "screen_brightness", current_brightness
                ], capture_output=True, timeout=2)
            except:
                pass

            # Method 2: Status bar flash
            try:
                subprocess.run([
                    "adb", "-s", device_serial, "shell",
                    "cmd", "statusbar", "expand-notifications"
                ], capture_output=True, timeout=2)

                await asyncio.sleep(0.2)

                subprocess.run([
                    "adb", "-s", device_serial, "shell",
                    "cmd", "statusbar", "collapse"
                ], capture_output=True, timeout=2)
            except:
                pass

        except Exception as e:
            logger.debug(f"Error creating enhanced screen flash: {e}")

    async def _create_android_action_label(self, action_type, element_text, device_serial, element):
        """Create floating action label - mobile equivalent of web floating labels"""
        try:
            # Create action message
            if element_text:
                short_text = element_text[:20] + "..." if len(element_text) > 20 else element_text
                if action_type == "CLICK":
                    message = f"🔴 CLICKING: {short_text}"
                elif action_type == "INPUT":
                    message = f"🟡 TYPING IN: {short_text}"
                elif action_type == "ASSERT":
                    message = f"🟢 VERIFYING: {short_text}"
                else:
                    message = f"🔴 {action_type}: {short_text}"
            else:
                message = f"🔴 AI {action_type}"

            console.print(f"[red]🏷️ Creating action label: {message}[/red]")

            # Method 1: Create persistent notification
            subprocess.run([
                "adb", "-s", device_serial, "shell",
                "cmd", "notification", "post", "-t", message,
                "--ongoing", "ai_action_label", message
            ], capture_output=True, timeout=3)

            # Method 2: Create toast message
            subprocess.run([
                "adb", "-s", device_serial, "shell",
                "am", "start", "-a", "android.intent.action.VIEW",
                "--es", "android.intent.extra.TEXT", message,
                "-t", "text/plain"
            ], capture_output=True, timeout=3)

            console.print(f"[red]✅ Action label created: {message}[/red]")

        except Exception as e:
            console.print(f"[red]❌ Error creating action label: {e}[/red]")
            logger.debug(f"Error creating action label: {e}")

    async def _create_screen_flash_effect(self, device_serial, action_type):
        """Create screen flash effect for attention - mobile equivalent of web animations"""
        try:
            console.print(f"[red]⚡ Creating screen flash for {action_type}[/red]")

            # Get current brightness
            result = subprocess.run([
                "adb", "-s", device_serial, "shell",
                "settings", "get", "system", "screen_brightness"
            ], capture_output=True, text=True, timeout=3)

            original_brightness = result.stdout.strip() if result.returncode == 0 else "128"

            # Flash sequence: bright -> normal -> bright -> normal
            flash_sequence = ["255", original_brightness, "255", original_brightness]

            for brightness in flash_sequence:
                subprocess.run([
                    "adb", "-s", device_serial, "shell",
                    "settings", "put", "system", "screen_brightness", brightness
                ], capture_output=True, timeout=2)
                await asyncio.sleep(0.1)

            console.print("[red]⚡ Screen flash completed[/red]")

        except Exception as e:
            console.print(f"[red]❌ Error creating screen flash: {e}[/red]")
            logger.debug(f"Error creating screen flash: {e}")

    async def _enable_visual_feedback_features(self, device_serial: str):
        """Enable visual feedback features on the device"""
        try:
            # Enable show taps (this will show red circles when tapping)
            subprocess.run([
                "adb", "-s", device_serial, "shell",
                "settings", "put", "system", "show_touches", "1"
            ], capture_output=True, timeout=2)

            # Disable pointer location (removes coordinate display clutter)
            subprocess.run([
                "adb", "-s", device_serial, "shell",
                "settings", "put", "system", "pointer_location", "0"
            ], capture_output=True, timeout=2)

            console.print("[red]👆 Enabled clean visual touch indicators (no coordinate clutter)[/red]")

        except Exception as e:
            console.print(f"[red]❌ Error enabling visual features: {e}[/red]")
            logger.debug(f"Error enabling visual features: {e}")

    async def _disable_visual_feedback_features(self, device_serial: str):
        """Disable visual feedback features after testing"""
        try:
            console.print("[cyan]🧹 Cleaning up visual feedback features[/cyan]")

            # Disable show touches
            subprocess.run([
                "adb", "-s", device_serial, "shell",
                "settings", "put", "system", "show_touches", "0"
            ], capture_output=True, timeout=2)

            console.print("[green]✅ Visual feedback features disabled[/green]")

        except Exception as e:
            console.print(f"[yellow]⚠️ Error disabling visual feedback: {e}[/yellow]")
            logger.debug(f"Error disabling visual feedback: {e}")

    async def _cleanup_pointer_location(self):
        """Clean up pointer location display on all connected devices"""
        try:
            device_serial = await self._get_device_serial()
            if device_serial:
                # Disable pointer location coordinates display
                subprocess.run([
                    "adb", "-s", device_serial, "shell",
                    "settings", "put", "system", "pointer_location", "0"
                ], capture_output=True, timeout=2)

                console.print("[green]🧹 Cleaned up pointer location coordinates display[/green]")

        except Exception as e:
            logger.debug(f"Error cleaning up pointer location: {e}")

    async def _create_visible_highlight_effect(self, element_bounds=None, action_type="click"):
        """Create a visible highlight effect using element-based highlighting"""
        try:
            console.print(f"[red]🎯 Creating element-based highlight for {action_type}[/red]")

            # Method 1: Use UI element highlighting instead of coordinates
            await self._highlight_ui_element_with_border(element_bounds, action_type)

            # Method 2: Create visual feedback using element interaction
            await self._create_element_visual_feedback(element_bounds, action_type)

        except Exception as e:
            logger.debug(f"Error creating visible highlight: {e}")

    async def _highlight_ui_element_with_border(self, element_bounds, action_type):
        """Create a red border highlight around the actual UI element"""
        try:
            if not self.device:
                return

            console.print(f"[red]🔴 Adding red border highlight for {action_type}[/red]")

            # Method 1: Use uiautomator2 to highlight the element
            if hasattr(element_bounds, 'click'):
                # This is an actual UI element object
                element = element_bounds

                # Get element info for highlighting
                try:
                    element_info = element.info
                    bounds = element_info.get('bounds', {})
                    text = element_info.get('text', '')
                    description = element_info.get('contentDescription', '')

                    console.print(f"[red]📍 Highlighting element: '{text or description}'[/red]")

                    # Create visual highlight by briefly changing element state
                    await self._create_element_state_highlight(element, action_type)

                except Exception as e:
                    logger.debug(f"Error getting element info: {e}")

            # Method 2: Use element selector highlighting
            await self._create_selector_based_highlight(element_bounds, action_type)

        except Exception as e:
            logger.debug(f"Error highlighting UI element: {e}")

    async def _create_element_state_highlight(self, element, action_type):
        """Create highlight by manipulating element state"""
        try:
            # Method 1: Long press to show element selection
            if action_type in ["CLICK", "INPUT"]:
                console.print("[red]🔴 Creating long press highlight[/red]")
                element.long_click(duration=0.5)  # Brief long press for highlight
                await asyncio.sleep(0.3)

            # Method 2: Focus the element to create visual feedback
            try:
                if hasattr(element, 'set_focus'):
                    element.set_focus()
                    await asyncio.sleep(0.5)
            except:
                pass

            # Method 3: Create selection highlight for text elements
            if action_type == "ASSERT" and hasattr(element, 'get_text'):
                try:
                    # Select text to create visual highlight
                    if element.get_text():
                        console.print("[red]🔍 Creating text selection highlight[/red]")
                        # Double tap to select text (creates visual highlight)
                        element.double_click()
                        await asyncio.sleep(0.5)
                except:
                    pass

        except Exception as e:
            logger.debug(f"Error creating element state highlight: {e}")

    async def _create_selector_based_highlight(self, element_bounds, action_type):
        """Create highlight using element selectors"""
        try:
            if not self.device:
                return

            # Method 1: Use accessibility highlighting
            device_serial = await self._get_device_serial()
            if device_serial:
                # Enable accessibility highlighting
                subprocess.run([
                    "adb", "-s", device_serial, "shell",
                    "settings", "put", "secure", "accessibility_enabled", "1"
                ], capture_output=True, timeout=2)

                # Trigger accessibility focus (creates visual highlight)
                subprocess.run([
                    "adb", "-s", device_serial, "shell",
                    "input", "keyevent", "KEYCODE_DPAD_CENTER"
                ], capture_output=True, timeout=1)

                await asyncio.sleep(0.5)

                console.print("[red]♿ Created accessibility highlight[/red]")

        except Exception as e:
            logger.debug(f"Error creating selector highlight: {e}")

    async def _create_element_visual_feedback(self, element_bounds, action_type):
        """Create visual feedback specific to the element being interacted with"""
        try:
            console.print(f"[red]✨ Creating visual feedback for {action_type}[/red]")

            # Method 1: Show toast with element information
            if hasattr(element_bounds, 'info'):
                element_info = element_bounds.info
                text = element_info.get('text', '')
                description = element_info.get('contentDescription', '')
                element_name = text or description or 'UI Element'

                device_serial = await self._get_device_serial()
                if device_serial:
                    toast_message = f"🔴 AI {action_type}: {element_name[:30]}"
                    subprocess.run([
                        "adb", "-s", device_serial, "shell",
                        "am", "start", "-a", "android.intent.action.VIEW",
                        "-d", f"content://toast/{toast_message}"
                    ], capture_output=True, timeout=3)

                    console.print(f"[red]📢 Toast: {toast_message}[/red]")

            # Method 2: Create visual pulse effect on the element
            if hasattr(element_bounds, 'click'):
                await self._create_element_pulse_effect(element_bounds, action_type)

        except Exception as e:
            logger.debug(f"Error creating element visual feedback: {e}")

    async def _create_element_pulse_effect(self, element, action_type):
        """Create a pulsing effect on the specific element"""
        try:
            console.print(f"[red]💓 Creating pulse effect for {action_type}[/red]")

            # Create pulsing effect by rapid interactions
            for pulse in range(2):  # 2 pulses
                if action_type == "CLICK":
                    # Brief touch and release for visual feedback
                    try:
                        element.touch()  # Touch without click
                        await asyncio.sleep(0.1)
                    except:
                        pass
                elif action_type == "ASSERT":
                    # Brief focus for assertion highlight
                    try:
                        if hasattr(element, 'set_focus'):
                            element.set_focus()
                        await asyncio.sleep(0.1)
                    except:
                        pass

                await asyncio.sleep(0.2)  # Pause between pulses

        except Exception as e:
            logger.debug(f"Error creating pulse effect: {e}")

    async def _create_circle_highlight_pattern(self, device_serial: str, center_x: int, center_y: int):
        """Create a circle pattern of taps around the target"""
        try:
            import math

            # Create circle of taps around the center point
            radius = 30  # 30 pixel radius
            num_points = 8  # 8 points around the circle

            for i in range(num_points):
                angle = (2 * math.pi * i) / num_points
                x = center_x + int(radius * math.cos(angle))
                y = center_y + int(radius * math.sin(angle))

                subprocess.run([
                    "adb", "-s", device_serial, "shell", "input", "tap", str(x), str(y)
                ], capture_output=True, timeout=1)
                await asyncio.sleep(0.05)  # Quick taps

            console.print("[red]⭕ Created circle highlight pattern[/red]")

        except Exception as e:
            logger.debug(f"Error creating circle pattern: {e}")

    async def _add_screen_overlay(self, element_bounds=None, action_type="click"):
        """Add red overlay/border around the element being interacted with"""
        try:
            device_serial = await self._get_device_serial()
            if not device_serial:
                return

            console.print(f"[red]🎯 Adding visual highlight for {action_type}[/red]")

            # Method 1: Create red overlay using ADB shell commands
            await self._create_red_overlay_effect(device_serial, element_bounds, action_type)

            # Method 2: Add visual border around element
            if element_bounds:
                await self._draw_element_border(device_serial, element_bounds)

            # Method 3: Create pulsing effect
            await self._create_pulsing_highlight(device_serial, element_bounds)

        except Exception as e:
            logger.debug(f"Error adding screen overlay: {e}")

    async def _create_red_overlay_effect(self, device_serial: str, element_bounds=None, action_type="click"):
        """Create a red overlay effect on the screen"""
        try:
            # Method 1: Use notification bar to show red indicator
            subprocess.run([
                "adb", "-s", device_serial, "shell",
                "cmd", "notification", "post", "-t", f"AI {action_type.upper()}",
                "ai_action", f"🔴 AI performing {action_type}"
            ], capture_output=True, timeout=3)

            # Method 2: Change status bar color temporarily
            try:
                # Set status bar to red
                subprocess.run([
                    "adb", "-s", device_serial, "shell",
                    "cmd", "overlay", "enable", "com.android.theme.color.red"
                ], capture_output=True, timeout=2)

                await asyncio.sleep(0.5)  # Show red for half second

                # Reset status bar
                subprocess.run([
                    "adb", "-s", device_serial, "shell",
                    "cmd", "overlay", "disable", "com.android.theme.color.red"
                ], capture_output=True, timeout=2)
            except:
                pass

        except Exception as e:
            logger.debug(f"Error creating red overlay: {e}")

    async def _draw_element_border(self, device_serial: str, element_bounds):
        """Draw a red border around the target element"""
        try:
            if not element_bounds:
                return

            # Parse element bounds
            bounds_info = await self._parse_element_bounds(element_bounds)
            if not bounds_info:
                return

            x1, y1, x2, y2 = bounds_info

            console.print(f"[red]📍 Drawing border around element: ({x1},{y1}) to ({x2},{y2})[/red]")

            # Draw border by tapping around the element perimeter
            border_points = [
                # Top border
                (x1, y1), (x1 + (x2-x1)//4, y1), (x1 + (x2-x1)//2, y1), (x1 + 3*(x2-x1)//4, y1), (x2, y1),
                # Right border
                (x2, y1 + (y2-y1)//4), (x2, y1 + (y2-y1)//2), (x2, y1 + 3*(y2-y1)//4), (x2, y2),
                # Bottom border
                (x1 + 3*(x2-x1)//4, y2), (x1 + (x2-x1)//2, y2), (x1 + (x2-x1)//4, y2), (x1, y2),
                # Left border
                (x1, y1 + 3*(y2-y1)//4), (x1, y1 + (y2-y1)//2), (x1, y1 + (y2-y1)//4)
            ]

            # Rapidly tap border points to create visual border effect
            for x, y in border_points:
                subprocess.run([
                    "adb", "-s", device_serial, "shell", "input", "tap", str(x), str(y)
                ], capture_output=True, timeout=1)
                await asyncio.sleep(0.05)  # Very quick taps

        except Exception as e:
            logger.debug(f"Error drawing element border: {e}")

    async def _create_pulsing_highlight(self, device_serial: str, element_bounds):
        """Create a pulsing highlight effect"""
        try:
            bounds_info = await self._parse_element_bounds(element_bounds)
            if not bounds_info:
                return

            x1, y1, x2, y2 = bounds_info
            center_x = (x1 + x2) // 2
            center_y = (y1 + y2) // 2

            console.print(f"[red]💓 Creating pulsing effect at ({center_x}, {center_y})[/red]")

            # Create pulsing effect with multiple rapid taps at center
            for pulse in range(3):  # 3 pulses
                # Rapid taps in expanding circle
                for radius in [5, 10, 15, 20]:
                    for angle in [0, 90, 180, 270]:  # 4 points around circle
                        import math
                        x = center_x + int(radius * math.cos(math.radians(angle)))
                        y = center_y + int(radius * math.sin(math.radians(angle)))

                        subprocess.run([
                            "adb", "-s", device_serial, "shell", "input", "tap", str(x), str(y)
                        ], capture_output=True, timeout=1)

                await asyncio.sleep(0.2)  # Pause between pulses

        except Exception as e:
            logger.debug(f"Error creating pulsing highlight: {e}")

    async def _parse_element_bounds(self, element_bounds):
        """Parse element bounds from various formats"""
        try:
            if not element_bounds:
                return None

            # Try to get bounds from uiautomator2 element
            if hasattr(element_bounds, 'bounds'):
                bounds = element_bounds.bounds
                return (bounds[0], bounds[1], bounds[2], bounds[3])

            # Try to parse string format "[x1,y1][x2,y2]"
            import re
            bounds_match = re.findall(r'\[(\d+),(\d+)\]\[(\d+),(\d+)\]', str(element_bounds))
            if bounds_match:
                x1, y1, x2, y2 = map(int, bounds_match[0])
                return (x1, y1, x2, y2)

            # Try to parse dict format
            if isinstance(element_bounds, dict):
                if all(key in element_bounds for key in ['left', 'top', 'right', 'bottom']):
                    return (element_bounds['left'], element_bounds['top'],
                           element_bounds['right'], element_bounds['bottom'])

            return None

        except Exception as e:
            logger.debug(f"Error parsing element bounds: {e}")
            return None

    async def _show_action_toast(self, action_type="click", element_text=""):
        """Show toast notification indicating AI action with element info"""
        try:
            device_serial = await self._get_device_serial()
            if not device_serial:
                return

            # Create meaningful toast message with element info
            if element_text:
                # Truncate long text for toast
                short_text = element_text[:20] + "..." if len(element_text) > 20 else element_text
                toast_message = f"🔴 AI {action_type.upper()}: {short_text}"
            else:
                toast_message = f"🔴 AI {action_type.upper()}"

            console.print(f"[red]📢 Showing toast: {toast_message}[/red]")

            # Method 1: Use am command to show toast
            subprocess.run([
                "adb", "-s", device_serial, "shell",
                "am", "start", "-a", "android.intent.action.VIEW",
                "-d", f"content://toast/{toast_message}"
            ], capture_output=True, timeout=3)

            # Method 2: Use input keyevent to show notification
            subprocess.run([
                "adb", "-s", device_serial, "shell",
                "cmd", "notification", "post", "-t", toast_message,
                "ai_highlight", f"AI performing {action_type} on {element_text or 'element'}"
            ], capture_output=True, timeout=3)

            # Method 3: Create visible screen effect using input events
            await self._create_screen_flash_effect(device_serial, action_type)

        except Exception as e:
            logger.debug(f"Error showing action toast: {e}")

    async def _create_screen_flash_effect(self, device_serial: str, action_type: str):
        """Create a visible screen flash effect WITHOUT using volume keys"""
        try:
            console.print(f"[red]⚡ Creating screen flash for {action_type}[/red]")

            # Method 1: Brightness flash (safer than power button)
            try:
                # Get current brightness
                result = subprocess.run([
                    "adb", "-s", device_serial, "shell",
                    "settings", "get", "system", "screen_brightness"
                ], capture_output=True, text=True, timeout=3)

                current_brightness = result.stdout.strip() if result.returncode == 0 else "128"

                # Flash to max brightness briefly
                subprocess.run([
                    "adb", "-s", device_serial, "shell",
                    "settings", "put", "system", "screen_brightness", "255"
                ], capture_output=True, timeout=2)

                await asyncio.sleep(0.2)  # Brief flash

                # Restore original brightness
                subprocess.run([
                    "adb", "-s", device_serial, "shell",
                    "settings", "put", "system", "screen_brightness", current_brightness
                ], capture_output=True, timeout=2)
            except:
                pass

            # Method 2: Create visual pattern instead of volume keys
            screen_center_x, screen_center_y = await self._get_screen_center(device_serial)

            # Create quick flash pattern with taps
            for _ in range(3):
                subprocess.run([
                    "adb", "-s", device_serial, "shell", "input", "tap",
                    str(screen_center_x), str(screen_center_y)
                ], capture_output=True, timeout=1)
                await asyncio.sleep(0.05)

        except Exception as e:
            logger.debug(f"Error creating screen flash: {e}")

    async def _add_screen_flash(self):
        """Add screen flash effect to indicate AI action"""
        try:
            device_serial = await self._get_device_serial()
            if not device_serial:
                return

            console.print("[red]⚡ Creating visible screen flash effect[/red]")

            # Method 1: Create red screen overlay using developer options
            await self._enable_visual_feedback_overlay(device_serial)

            # Method 2: Use accessibility features for visual feedback
            await self._trigger_accessibility_highlight(device_serial)

            # Method 3: Create visible screen effect using rapid taps
            await self._create_rapid_tap_pattern(device_serial)

        except Exception as e:
            logger.debug(f"Error adding screen flash: {e}")

    async def _enable_visual_feedback_overlay(self, device_serial: str):
        """Enable visual feedback overlay on the device"""
        try:
            # Enable show taps in developer options
            subprocess.run([
                "adb", "-s", device_serial, "shell",
                "settings", "put", "system", "show_touches", "1"
            ], capture_output=True, timeout=2)

            # Enable pointer location
            subprocess.run([
                "adb", "-s", device_serial, "shell",
                "settings", "put", "system", "pointer_location", "1"
            ], capture_output=True, timeout=2)

            console.print("[red]👆 Enabled visual touch feedback[/red]")

        except Exception as e:
            logger.debug(f"Error enabling visual overlay: {e}")

    async def _trigger_accessibility_highlight(self, device_serial: str):
        """Trigger accessibility highlighting features"""
        try:
            # Enable accessibility highlighting
            subprocess.run([
                "adb", "-s", device_serial, "shell",
                "settings", "put", "secure", "accessibility_display_magnification_enabled", "1"
            ], capture_output=True, timeout=2)

            # Trigger magnification gesture (triple tap)
            screen_center_x, screen_center_y = await self._get_screen_center(device_serial)

            for _ in range(3):  # Triple tap
                subprocess.run([
                    "adb", "-s", device_serial, "shell", "input", "tap",
                    str(screen_center_x), str(screen_center_y)
                ], capture_output=True, timeout=1)
                await asyncio.sleep(0.1)

            await asyncio.sleep(0.5)

            # Disable magnification
            subprocess.run([
                "adb", "-s", device_serial, "shell",
                "settings", "put", "secure", "accessibility_display_magnification_enabled", "0"
            ], capture_output=True, timeout=2)

        except Exception as e:
            logger.debug(f"Error triggering accessibility highlight: {e}")

    async def _create_rapid_tap_pattern(self, device_serial: str):
        """Create a rapid tap pattern for visual feedback"""
        try:
            screen_center_x, screen_center_y = await self._get_screen_center(device_serial)

            # Create a visible pattern of taps
            tap_pattern = [
                (screen_center_x - 50, screen_center_y - 50),  # Top-left
                (screen_center_x + 50, screen_center_y - 50),  # Top-right
                (screen_center_x + 50, screen_center_y + 50),  # Bottom-right
                (screen_center_x - 50, screen_center_y + 50),  # Bottom-left
                (screen_center_x, screen_center_y)             # Center
            ]

            # Rapid taps in pattern
            for x, y in tap_pattern:
                subprocess.run([
                    "adb", "-s", device_serial, "shell", "input", "tap", str(x), str(y)
                ], capture_output=True, timeout=1)
                await asyncio.sleep(0.05)

            console.print("[red]🎯 Created rapid tap pattern for visual feedback[/red]")

        except Exception as e:
            logger.debug(f"Error creating tap pattern: {e}")

    async def _get_screen_center(self, device_serial: str):
        """Get the center coordinates of the screen"""
        try:
            # Get screen size
            result = subprocess.run([
                "adb", "-s", device_serial, "shell", "wm", "size"
            ], capture_output=True, text=True, timeout=3)

            if result.returncode == 0:
                # Parse output like "Physical size: 1080x2340"
                import re
                size_match = re.search(r'(\d+)x(\d+)', result.stdout)
                if size_match:
                    width, height = map(int, size_match.groups())
                    return width // 2, height // 2

            # Default fallback
            return 540, 1170

        except Exception as e:
            logger.debug(f"Error getting screen center: {e}")
            return 540, 1170  # Default center for common screen size

    async def _get_device_serial(self):
        """Get device serial for ADB commands"""
        try:
            # Try from android manager first
            if hasattr(self, 'android_manager') and self.android_manager:
                devices = await self.android_manager.get_connected_devices()
                if devices:
                    if isinstance(devices[0], dict):
                        # The android_manager stores device ID in "id" key, not "serial"
                        device_serial = devices[0].get("id") or devices[0].get("serial")
                        if device_serial:
                            return device_serial
                    else:
                        return devices[0]

            # Fallback: direct ADB
            result = subprocess.run(["adb", "devices"], capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')[1:]
                devices = [line.split()[0] for line in lines if line.strip() and 'device' in line]
                if devices:
                    return devices[0]

        except Exception as e:
            logger.debug(f"Error getting device serial: {e}")

        return None

    async def _add_visual_highlight_for_assertion(self, strategy: str, value: str):
        """Add visual highlight for assertion/verification actions"""
        try:
            if not self.visual_feedback_enabled:
                return

            console.print(f"[yellow]🔍 AI ASSERTION: Verifying {strategy}='{value}'[/yellow]")

            # Get element for highlighting
            element = None
            if strategy == "text" and self.device:
                try:
                    if self.device(text=value).exists:
                        element = self.device(text=value)
                except:
                    pass
            elif strategy == "description" and self.device:
                try:
                    if self.device(description=value).exists:
                        element = self.device(description=value)
                except:
                    pass
            elif strategy == "resource_id" and self.device:
                try:
                    if self.device(resourceId=value).exists:
                        element = self.device(resourceId=value)
                except:
                    pass

            # Add visual highlight for assertion with actual element and text
            if element:
                await self._add_visual_highlight(element, "ASSERT", value)
            else:
                # Fallback visual highlight without element
                await self._add_visual_highlight(None, "ASSERT", value)

            # Brief pause to show assertion highlight
            await asyncio.sleep(1.0)

        except Exception as e:
            logger.debug(f"Error adding assertion highlight: {e}")

    async def _stop_scenario_recording_and_wait(self) -> str:
        """Stop recording and wait for video generation to complete"""
        try:
            console.print("[cyan]🛑 Stopping recording and generating video...[/cyan]")

            # Stop the recording
            video_path = await self._stop_scenario_recording()

            if video_path:
                console.print("[cyan]⏳ Waiting for video generation to complete...[/cyan]")
                # Wait a bit more for file to be fully written and transferred
                await asyncio.sleep(2)

                # Verify video file exists and has content
                if os.path.exists(video_path):
                    file_size = os.path.getsize(video_path)
                    console.print(f"[green]✅ Video generation complete: {os.path.basename(video_path)} ({file_size} bytes)[/green]")
                else:
                    console.print(f"[yellow]⚠️ Video file not found: {video_path}[/yellow]")

                return video_path
            else:
                console.print("[red]❌ Failed to generate video[/red]")
                return ""

        except Exception as e:
            logger.error(f"Error stopping recording and waiting: {e}")
            return ""

    async def _stop_recording(self):
        """Stop video recording (legacy method)"""
        try:
            if self.recording:
                self.recording = False
                logger.info("Stopped recording")

        except Exception as e:
            logger.warning(f"Failed to stop recording: {e}")

    async def _generate_test_report(self, test_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate test execution report"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_file = f"test_report_{timestamp}.html"
            report_path = os.path.join(self.config.get("ANALYSIS_OUTPUT_PATH"), report_file)

            # Generate HTML report
            html_content = self._generate_html_report(test_results)

            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(html_content)

            console.print(f"[green]Test report generated: {report_file}[/green]")

            return {"success": True, "report_file": report_path}

        except Exception as e:
            logger.error(f"Failed to generate test report: {e}")
            return {"success": False, "error": str(e)}

    def _generate_html_report(self, test_results: List[Dict[str, Any]]) -> str:
        """Generate HTML test report"""
        total_tests = len(test_results)
        passed_tests = sum(1 for r in test_results if r["status"] == "PASSED")
        failed_tests = total_tests - passed_tests

        html = """
<!DOCTYPE html>
<html>
<head>
    <title>sMTm Test Report</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
        .summary {{ margin: 20px 0; }}
        .test-result {{ margin: 10px 0; padding: 10px; border-radius: 5px; }}
        .passed {{ background-color: #d4edda; }}
        .failed {{ background-color: #f8d7da; }}
        .details {{ margin-top: 10px; font-size: 0.9em; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>sMTm Mobile Test Report</h1>
        <p>Generated: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</p>
    </div>

    <div class="summary">
        <h2>Test Summary</h2>
        <p>Total Tests: {total_tests}</p>
        <p>Passed: {passed_tests}</p>
        <p>Failed: {failed_tests}</p>
        <p>Success Rate: {(passed_tests/total_tests*100):.1f}%</p>
    </div>

    <div class="results">
        <h2>Test Results</h2>
"""

        for result in test_results:
            status_class = "passed" if result["status"] == "PASSED" else "failed"
            html += """
        <div class="test-result {status_class}">
            <h3>{result['scenario']} - {result['status']}</h3>
            <div class="details">
                <p>File: {result['test_file']}</p>
                <p>Timestamp: {result['timestamp']}</p>
"""

            if result.get("steps_executed"):
                html += f"<p>Steps: {result['steps_passed']}/{result['steps_executed']} passed</p>"

            if result.get("error"):
                html += f"<p>Error: {result['error']}</p>"

            html += """
            </div>
        </div>
"""

        html += """
    </div>
</body>
</html>
"""

        return html

    async def _interactive_app_opening(self, mobile_os_callback=None) -> Dict[str, Any]:
        """Interactive app opening like ai-analysis"""
        try:
            console.print("[cyan]🚀 Mobile Test Application Opening[/cyan]")

            # Ask if user wants to open the mobile application
            console.print("\n[yellow]Should I open the mobile application for testing?[/yellow]")
            console.print("[cyan]1. Yes, open the application[/cyan]")
            console.print("[cyan]2. Application is already open[/cyan]")
            console.print("[cyan]3. Cancel testing[/cyan]")

            # Use callback if available, otherwise fallback to input
            if mobile_os_callback:
                choice = await mobile_os_callback()
            else:
                choice = input("\nEnter your choice (1-3): ").strip()

            if choice == "3":
                return {"success": False, "error": "Testing cancelled by user"}
            elif choice == "2":
                console.print("[green]✅ Proceeding with already open application[/green]")
                return {"success": True, "app_opened": False}
            elif choice == "1":
                # Get app package from latest analysis or ask user
                app_package = await self._get_target_app_package(mobile_os_callback)
                if not app_package:
                    return {"success": False, "error": "No app package found"}

                console.print(f"[yellow]📱 Opening application: {app_package}[/yellow]")

                # Open the application
                try:
                    self.device.app_start(app_package)
                    await asyncio.sleep(3)  # Wait for app to load

                    # Verify app opened successfully
                    current_app = self.device.app_current()
                    if current_app and app_package in current_app.get("package", ""):
                        console.print(f"[green]✅ Application opened successfully: {app_package}[/green]")
                        # Store app package for state management
                        self.app_package = app_package
                        # Detect initial page
                        self.current_page = await self._detect_current_page()
                        return {"success": True, "app_opened": True, "package": app_package}
                    else:
                        console.print(f"[red]❌ Failed to open application: {app_package}[/red]")
                        return {"success": False, "error": f"Failed to open application: {app_package}"}

                except Exception as e:
                    console.print(f"[red]❌ Error opening application: {e}[/red]")
                    return {"success": False, "error": f"Error opening application: {str(e)}"}
            else:
                console.print("[red]❌ Invalid choice[/red]")
                return {"success": False, "error": "Invalid choice"}

        except Exception as e:
            logger.error(f"Interactive app opening failed: {e}")
            return {"success": False, "error": f"Interactive app opening failed: {str(e)}"}

    async def _get_target_app_package(self, mobile_os_callback=None) -> Optional[str]:
        """Get target app package by scanning device and letting user choose"""
        try:
            # First try to get from latest analysis file
            analysis_files = glob.glob("./data/analysis/*.json")
            if analysis_files:
                latest_file = max(analysis_files, key=os.path.getctime)
                with open(latest_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                # Extract package from metadata
                if 'metadata' in data and 'package' in data['metadata']:
                    package = data['metadata']['package']
                    if package and package != 'com.unknown.app':
                        console.print(f"[cyan]📦 Found package in analysis: {package}[/cyan]")
                        return package

            # If no package in analysis, scan device for installed apps
            console.print("[yellow]⚠️ No package found in analysis data[/yellow]")
            console.print("[cyan]🔍 Scanning device for installed applications...[/cyan]")

            installed_apps = await self._get_installed_apps()
            if not installed_apps:
                console.print("[red]❌ No applications found on device[/red]")
                return None

            # Display apps for user selection
            console.print("\n[yellow]📱 Available applications on device:[/yellow]")
            for i, app in enumerate(installed_apps, 1):
                app_name = app.get('name', 'Unknown App')
                package_name = app.get('package', 'unknown.package')
                console.print(f"[cyan]{i}. {app_name}[/cyan] [dim]({package_name})[/dim]")

            console.print(f"[cyan]{len(installed_apps) + 1}. Enter custom package name[/cyan]")

            # Get user choice
            if mobile_os_callback:
                choice = await mobile_os_callback()
            else:
                choice = input(f"\nSelect application (1-{len(installed_apps) + 1}): ").strip()

            # Process user choice
            try:
                choice_num = int(choice)
                if 1 <= choice_num <= len(installed_apps):
                    selected_app = installed_apps[choice_num - 1]
                    package = selected_app['package']
                    console.print(f"[green]✅ Selected: {selected_app['name']} ({package})[/green]")
                    return package
                elif choice_num == len(installed_apps) + 1:
                    # Custom package name
                    console.print("[cyan]Please enter the app package name (e.g., com.example.app):[/cyan]")
                    if mobile_os_callback:
                        package = await mobile_os_callback()
                    else:
                        package = input("App package: ").strip()
                    return package if package else None
                else:
                    console.print("[red]❌ Invalid selection[/red]")
                    return None
            except ValueError:
                console.print("[red]❌ Invalid input. Please enter a number.[/red]")
                return None

        except Exception as e:
            logger.error(f"Failed to get target app package: {e}")
            return None

    async def _get_installed_apps(self) -> List[Dict[str, str]]:
        """Get list of installed applications on the device"""
        try:
            if not self.device:
                return []

            console.print("[cyan]📱 Scanning device for applications...[/cyan]")

            # Get list of installed packages using adb
            import subprocess
            result = subprocess.run(
                ['adb', 'shell', 'pm', 'list', 'packages', '-3'],  # -3 for third-party apps only
                capture_output=True,
                text=True,
                timeout=30
            )

            if result.returncode != 0:
                console.print("[yellow]⚠️ Could not get package list, trying alternative method...[/yellow]")
                return await self._get_installed_apps_fallback()

            packages = []
            for line in result.stdout.strip().split('\n'):
                if line.startswith('package:'):
                    package_name = line.replace('package:', '').strip()
                    if package_name:
                        # Get app name using dumpsys
                        app_name = await self._get_app_name(package_name)
                        packages.append({
                            'name': app_name,
                            'package': package_name
                        })

            # Sort by app name for better user experience
            packages.sort(key=lambda x: x['name'].lower())

            console.print(f"[green]✅ Found {len(packages)} installed applications[/green]")
            return packages

        except Exception as e:
            logger.error(f"Failed to get installed apps: {e}")
            console.print("[yellow]⚠️ Could not scan device apps, using fallback method...[/yellow]")
            return await self._get_installed_apps_fallback()

    async def _get_installed_apps_fallback(self) -> List[Dict[str, str]]:
        """Fallback method to get common apps"""
        return [
            {'name': 'Chrome Browser', 'package': 'com.android.chrome'},
            {'name': 'Gmail', 'package': 'com.google.android.gm'},
            {'name': 'Google Play Store', 'package': 'com.android.vending'},
            {'name': 'Settings', 'package': 'com.android.settings'},
            {'name': 'Calculator', 'package': 'com.google.android.calculator'},
            {'name': 'Camera', 'package': 'com.google.android.GoogleCamera'},
            {'name': 'Maps', 'package': 'com.google.android.apps.maps'},
            {'name': 'YouTube', 'package': 'com.google.android.youtube'},
        ]

    async def _get_app_name(self, package_name: str) -> str:
        """Get human-readable app name from package name"""
        try:
            import subprocess
            result = subprocess.run(
                ['adb', 'shell', 'dumpsys', 'package', package_name, '|', 'grep', '-E', '"(applicationLabel|label)"'],
                capture_output=True,
                text=True,
                timeout=10,
                shell=True
            )

            if result.returncode == 0 and result.stdout:
                # Extract app name from dumpsys output
                for line in result.stdout.split('\n'):
                    if 'applicationLabel' in line or 'label' in line:
                        # Extract the label value
                        if '=' in line:
                            name = line.split('=')[-1].strip().strip('"')
                            if name and name != package_name:
                                return name

            # Fallback: create readable name from package
            return self._package_to_readable_name(package_name)

        except Exception:
            return self._package_to_readable_name(package_name)

    def _package_to_readable_name(self, package_name: str) -> str:
        """Convert package name to readable app name"""
        try:
            # Extract the last part of the package name and make it readable
            parts = package_name.split('.')
            if len(parts) > 0:
                app_part = parts[-1]
                # Capitalize and add spaces for camelCase
                import re
                readable = re.sub(r'([a-z])([A-Z])', r'\1 \2', app_part)
                return readable.title()
            return package_name
        except Exception:
            return package_name

    async def _load_latest_analysis_data(self) -> Optional[Dict[str, Any]]:
        """Load latest analysis data for element locators"""
        try:
            console.print("[cyan]🔍 Loading latest analysis data for element locators...[/cyan]")

            # Find analysis JSON files and prioritize by element quality
            analysis_files = glob.glob("./data/analysis/*_live_analysis.json")
            if not analysis_files:
                analysis_files = glob.glob("./data/analysis/*.json")

            if not analysis_files:
                console.print("[yellow]⚠️ No analysis data found. Element locators may not be optimal.[/yellow]")
                return None

            # Use only the latest JSON file as requested by user
            console.print(f"[cyan]🔍 Using latest JSON file from {len(analysis_files)} analysis files...[/cyan]")

            # Find the most recent file by modification time
            best_file = max(analysis_files, key=os.path.getctime)
            console.print(f"[green]📅 Selected latest file: {os.path.basename(best_file)}[/green]")

            console.print(f"[cyan]📄 Loading analysis data from: {os.path.basename(best_file)}[/cyan]")

            # Store all analysis files for potential element lookup
            self.all_analysis_files = analysis_files
            self.current_analysis_file = best_file

            with open(best_file, 'r', encoding='utf-8') as f:
                analysis_data = json.load(f)

            # Extract elements for locator mapping - try both structures
            elements = analysis_data.get('elements', [])
            if not elements:
                elements = analysis_data.get('analysis_data', [])
            console.print(f"[green]✅ Loaded {len(elements)} elements from analysis data[/green]")

            # Create element locator mapping for quick lookup
            element_locators = {}
            for element in elements:
                # Use text if available, otherwise use content_desc, otherwise use Element_Name
                # Handle None values properly
                text = (element.get('text') or '').strip()
                content_desc = (element.get('content_desc') or '').strip()
                element_name = (element.get('Element_Name') or '').strip()

                # Create mapping key - prioritize text, then content_desc, then Element_Name
                mapping_key = None
                if text:
                    mapping_key = text
                elif content_desc:
                    mapping_key = content_desc
                elif element_name:
                    mapping_key = element_name

                if mapping_key:
                    element_locators[mapping_key] = {
                        'resource_id': (element.get('resource_id') or ''),
                        'content_desc': content_desc,
                        'accessibility_id': (element.get('accessibility_id') or ''),
                        'class_name': (element.get('class_name') or ''),
                        'xpath': (element.get('xpath') or ''),
                        'bounds': (element.get('bounds') or ''),
                        'clickable': element.get('clickable', False),
                        'enabled': element.get('enabled', False),
                        'text': text
                    }

                    # Also create mapping for content_desc if different from text
                    if content_desc and content_desc != mapping_key:
                        element_locators[content_desc] = element_locators[mapping_key]

                    # Also create mapping for Element_Name if different
                    if element_name and element_name != mapping_key:
                        element_locators[element_name] = element_locators[mapping_key]

            return {
                'elements': elements,
                'element_locators': element_locators,
                'metadata': analysis_data.get('metadata', {}),
                'file_path': best_file
            }

        except Exception as e:
            logger.error(f"Failed to load analysis data: {e}")
            console.print(f"[red]❌ Failed to load analysis data: {e}[/red]")
            return None

    async def _execute_test_files_with_analysis(self, test_files: List[str], analysis_data: Optional[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Execute test files with analysis data integration"""
        test_results = []

        # Store analysis data for use in step execution
        self.analysis_data = analysis_data

        for test_file in test_files:
            console.print(f"[cyan]Executing test file: {os.path.basename(test_file)}[/cyan]")

            try:
                # Start recording for this test
                await self._start_recording(test_file)

                # Parse and execute scenarios
                scenarios = await self._parse_gherkin_file(test_file)

                for scenario in scenarios:
                    self.scenario_count += 1
                    console.print(f"[cyan]🎬 Processing Scenario {self.scenario_count}: {scenario['name']}[/cyan]")

                    # Execute scenario with enhanced recording timing
                    result = await self._execute_scenario_with_enhanced_recording(scenario, test_file, analysis_data)

                    test_results.append(result)

                    # Reset first scenario flag after first scenario
                    if self.is_first_scenario:
                        self.is_first_scenario = False

                # Stop recording
                await self._stop_recording()

            except Exception as e:
                logger.error(f"Failed to execute test file {test_file}: {e}")
                test_results.append({
                    "test_file": test_file,
                    "scenario": "File Execution",
                    "status": "FAILED",
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                })

        return test_results

    async def _update_testsuite_results(self, test_results: List[Dict[str, Any]]):
        """Update testsuite.xlsx with test results and video recording paths"""
        try:
            testsuite_path = Path("testcases/testsuite.xlsx")
            if not testsuite_path.exists():
                console.print("[yellow]⚠️ testsuite.xlsx not found, skipping results update[/yellow]")
                return

            # Read current testsuite
            df = pd.read_excel(testsuite_path)
            console.print(f"[cyan]📊 Updating testsuite with {len(test_results)} test results[/cyan]")

            # Update each test result
            for result in test_results:
                scenario_name = result.get("scenario", "")
                status = "Passed" if result.get("status") == "PASSED" else "Failed"
                error_note = result.get("error", "")
                video_path = result.get("video_path", "")

                # Find matching row in testsuite
                mask = df['Scenario'].str.contains(scenario_name, case=False, na=False)
                matching_rows = df[mask]

                if not matching_rows.empty:
                    # Update the first matching row
                    idx = matching_rows.index[0]
                    df.loc[idx, 'Status'] = status

                    # Update Note column for failed tests
                    if status == "Failed" and error_note:
                        df.loc[idx, 'Note'] = f"Test failed: {error_note}"
                    elif status == "Passed":
                        df.loc[idx, 'Note'] = "Test passed successfully"

                    # Update Link_video column if video was recorded
                    if video_path:
                        # Store relative path
                        relative_video_path = os.path.relpath(video_path, "testcases")
                        df.loc[idx, 'Link_video'] = relative_video_path

                    console.print(f"[green]✅ Updated scenario: {scenario_name} - Status: {status}[/green]")
                else:
                    console.print(f"[yellow]⚠️ Scenario not found in testsuite: {scenario_name}[/yellow]")

            # Save updated testsuite
            df.to_excel(testsuite_path, index=False)
            console.print(f"[green]💾 Testsuite updated successfully: {testsuite_path}[/green]")

        except Exception as e:
            logger.error(f"Failed to update testsuite: {e}")
            console.print(f"[red]❌ Failed to update testsuite: {e}[/red]")

    async def _execute_scenario_with_enhanced_recording(self, scenario: Dict[str, Any], test_file: str, analysis_data: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Execute scenario with enhanced recording timing"""
        try:
            scenario_name = scenario.get("name", "Unknown Scenario")
            steps = scenario.get("steps", [])

            console.print(f"[cyan]🎬 Executing scenario: {scenario_name}[/cyan]")

            if not steps:
                return {
                    "scenario": scenario_name,
                    "status": "FAILED",
                    "error": "No steps found in scenario"
                }

            video_path = None

            # For first scenario: Start recording immediately when app opens
            if self.is_first_scenario:
                console.print("[cyan]🎥 First scenario - Starting recording immediately[/cyan]")
                video_path = await self._start_scenario_recording(scenario_name)
                await self._wait_for_recording_active()

            # Execute steps with enhanced recording timing
            for i, step in enumerate(steps, 1):
                console.print(f"[cyan]📱 Step {i}: {step}[/cyan]")

                # For subsequent scenarios: Start recording after first step
                if not self.is_first_scenario and i == 1:
                    console.print("[cyan]🎥 Executing first step before starting recording[/cyan]")

                    # Execute first step
                    step_result = await self._execute_step_with_analysis(step, i, analysis_data)
                    if not step_result.get("success", False):
                        return {
                            "scenario": scenario_name,
                            "status": "FAILED",
                            "error": f"Step {i} failed: {step_result.get('error', 'Unknown error')}"
                        }

                    # Start recording after first step
                    console.print("[cyan]🎥 Starting recording after first step[/cyan]")
                    video_path = await self._start_scenario_recording(scenario_name)
                    await self._wait_for_recording_active()

                else:
                    # Execute remaining steps normally (for first scenario: all steps, for subsequent: steps 2+)
                    step_result = await self._execute_step_with_analysis(step, i, analysis_data)
                    if not step_result.get("success", False):
                        # Wait for any page loading before stopping recording
                        if video_path:
                            console.print("[cyan]⏳ Waiting for page loading before stopping recording (failed scenario)...[/cyan]")
                            await self._wait_for_page_loading_complete()
                            final_video_path = await self._stop_scenario_recording_and_wait()

                        return {
                            "scenario": scenario_name,
                            "status": "FAILED",
                            "error": f"Step {i} failed: {step_result.get('error', 'Unknown error')}",
                            "video_path": final_video_path if video_path else None
                        }

            # Wait for page loading to complete before stopping recording
            final_video_path = None
            if video_path:
                console.print("[cyan]⏳ Waiting for page loading to complete...[/cyan]")
                await self._wait_for_page_loading_complete()

                console.print("[cyan]🎥 Stopping recording and waiting for generation...[/cyan]")
                final_video_path = await self._stop_scenario_recording_and_wait()

            return {
                "scenario": scenario_name,
                "status": "PASSED",
                "video_path": final_video_path
            }

        except Exception as e:
            logger.error(f"Scenario execution failed: {e}")

            # Wait for any page loading before stopping recording
            if video_path:
                console.print("[cyan]⏳ Waiting for page loading before stopping recording (exception)...[/cyan]")
                await self._wait_for_page_loading_complete()
                final_video_path = await self._stop_scenario_recording_and_wait()

            return {
                "scenario": scenario_name,
                "status": "FAILED",
                "error": str(e),
                "video_path": final_video_path if video_path else None
            }

    async def _execute_scenario_with_analysis(self, scenario: Dict[str, Any], test_file: str, analysis_data: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Execute a single test scenario with analysis data integration"""
        try:
            console.print(f"[yellow]Executing scenario: {scenario['name']}[/yellow]")

            result = {
                "test_file": os.path.basename(test_file),
                "scenario": scenario["name"],
                "status": "PASSED",
                "steps_executed": 0,
                "steps_passed": 0,
                "steps_failed": 0,
                "error": None,
                "timestamp": datetime.now().isoformat(),
                "screenshots": []
            }

            # Execute each step with enhanced element finding
            for i, step in enumerate(scenario["steps"]):
                console.print(f"[cyan]📱 Step {i+1}: {step}[/cyan]")

                # Add visual delay for user to see what's happening
                await asyncio.sleep(2)  # 2 second delay between steps for visibility

                step_result = await self._execute_step_with_analysis(step, i, analysis_data)

                result["steps_executed"] += 1

                if step_result["success"]:
                    result["steps_passed"] += 1
                    console.print(f"[green]✓ {step}[/green]")
                else:
                    result["steps_failed"] += 1
                    result["status"] = "FAILED"
                    result["error"] = step_result.get("error", "Step failed")
                    console.print(f"[red]✗ {step} - {result['error']}[/red]")

                    # Take screenshot on failure
                    if self.config.get("SCREENSHOT_ON_FAIL"):
                        screenshot_path = await self._take_screenshot(f"failure_{i}")
                        if screenshot_path:
                            result["screenshots"].append(screenshot_path)

                    break  # Stop on first failure

            return result

        except Exception as e:
            logger.error(f"Failed to execute scenario {scenario['name']}: {e}")
            return {
                "test_file": os.path.basename(test_file),
                "scenario": scenario["name"],
                "status": "FAILED",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    async def _execute_step_with_analysis(self, step: str, step_index: int, analysis_data: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Execute a single test step with enhanced element finding using analysis data"""
        try:
            step_lower = step.lower()
            console.print(f"[blue]🔍 Analyzing step: {step}[/blue]")

            # Show AI tooltip for step analysis
            await self._show_ai_tooltip("ANALYZE_STEP", step, "Parsing Gherkin syntax")

            # Handle UI state management for "Given" steps
            if step_lower.startswith("given") and "main page" in step_lower:
                return await self._step_ensure_main_page()

            # Parse step and determine action
            if "launch" in step_lower and "application" in step_lower:
                await self._show_ai_tooltip("LAUNCH_APP")
                return await self._step_launch_app_enhanced()

            elif "tap" in step_lower or "click" in step_lower:
                element_text = self._extract_element_from_step(step)
                await self._show_ai_tooltip("TAP_ELEMENT", element_text, "Using analysis data for precise targeting")
                result = await self._step_tap_element_enhanced(element_text, analysis_data)
                # Update navigation state after successful click
                if result["success"]:
                    await self._update_navigation_state(element_text)
                return result

            elif "enter" in step_lower and "in" in step_lower:
                text_to_enter = self._extract_text_from_step(step)
                element_text = self._extract_element_from_step(step)
                await self._show_ai_tooltip("ENTER_TEXT", element_text, text_to_enter)
                return await self._step_enter_text_enhanced(text_to_enter, element_text, analysis_data)

            elif "scroll" in step_lower:
                direction = "down" if "down" in step_lower else "up"
                await self._show_ai_tooltip("SCROLL", direction)
                return await self._step_scroll(direction)

            elif "should see" in step_lower or "assert" in step_lower:
                element_text = self._extract_element_from_step(step)
                await self._show_ai_tooltip("VERIFY_ELEMENT", element_text)
                return await self._step_verify_element_enhanced(element_text, analysis_data)

            elif "should be clickable" in step_lower or "clickable" in step_lower:
                element_text = self._extract_element_from_step(step)
                await self._show_ai_tooltip("VERIFY_ELEMENT", element_text)
                return await self._step_verify_element_enhanced(element_text, analysis_data)

            else:
                # Generic validation step
                return await self._step_generic_validation(step)

        except Exception as e:
            logger.error(f"Failed to execute step '{step}': {e}")
            return {"success": False, "error": str(e)}

    async def _step_tap_element_enhanced(self, element_text: str, analysis_data: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Enhanced tap element with analysis data integration"""
        try:
            if not element_text:
                return {"success": False, "error": "No element specified"}

            console.print(f"[cyan]🎯 Looking for element: '{element_text}'[/cyan]")

            # Try to get element locator from analysis data
            best_locator = self._get_best_locator_for_element(element_text, analysis_data)

            if best_locator:
                console.print(f"[green]📍 Using analysis data locator: {best_locator['strategy']}='{best_locator['value']}'[/green]")

                # Try the best locator first
                if await self._try_locator_strategy(best_locator['strategy'], best_locator['value']):
                    console.print(f"[green]✅ Successfully clicked element using {best_locator['strategy']}[/green]")
                    return {"success": True}

            # Fallback to original methods
            console.print("[yellow]⚠️ Analysis locator failed, trying fallback methods...[/yellow]")

            # Try cross-file search first before fallback methods
            cross_file_locator = self._get_best_locator_for_element(element_text, getattr(self, 'analysis_data', None))
            if cross_file_locator:
                console.print(f"[cyan]🎯 Found element in cross-file search, using {cross_file_locator['strategy']}[/cyan]")
                if await self._try_locator_strategy(cross_file_locator['strategy'], cross_file_locator['value']):
                    console.print(f"[green]✅ Successfully clicked element using cross-file {cross_file_locator['strategy']}[/green]")
                    return {"success": True}

            # Try multiple locator strategies in priority order
            strategies = [
                ("text", element_text),
                ("description", element_text),
                ("resource_id", element_text),
                ("xpath", f"//*[@text='{element_text}']"),
                ("xpath", f"//*[@content-desc='{element_text}']")
            ]

            for strategy, value in strategies:
                if await self._try_locator_strategy(strategy, value):
                    console.print(f"[green]✅ Successfully clicked element using {strategy}[/green]")
                    return {"success": True}

            return {"success": False, "error": f"Element '{element_text}' not found with any locator strategy"}

        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _step_enter_text_enhanced(self, text: str, element_text: str, analysis_data: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Enhanced text entry with analysis data integration"""
        try:
            console.print(f"[cyan]⌨️ Entering text '{text}' in element: '{element_text}'[/cyan]")

            # Try to get element locator from analysis data
            best_locator = self._get_best_locator_for_element(element_text, analysis_data)

            if best_locator:
                console.print(f"[green]📍 Using analysis data locator: {best_locator['strategy']}='{best_locator['value']}'[/green]")

                # Try the best locator first
                if await self._try_text_input_strategy(best_locator['strategy'], best_locator['value'], text):
                    console.print(f"[green]✅ Successfully entered text using {best_locator['strategy']}[/green]")
                    return {"success": True}

            # Fallback to original methods
            console.print("[yellow]⚠️ Analysis locator failed, trying fallback methods...[/yellow]")

            # Try multiple locator strategies
            strategies = [
                ("text", element_text),
                ("description", element_text),
                ("resource_id", element_text),
                ("xpath", f"//*[@text='{element_text}']"),
                ("class_name", "android.widget.EditText")
            ]

            for strategy, value in strategies:
                if await self._try_text_input_strategy(strategy, value, text):
                    console.print(f"[green]✅ Successfully entered text using {strategy}[/green]")
                    return {"success": True}

            return {"success": False, "error": f"Input element '{element_text}' not found"}

        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _step_verify_element_enhanced(self, element_text: str, analysis_data: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Enhanced element verification with analysis data integration"""
        try:
            if not element_text:
                return {"success": True}  # Generic validation

            console.print(f"[cyan]👁️ Verifying element exists: '{element_text}'[/cyan]")

            # Try to get element locator from analysis data
            best_locator = self._get_best_locator_for_element(element_text, analysis_data)

            if best_locator:
                console.print(f"[green]📍 Using analysis data locator: {best_locator['strategy']}='{best_locator['value']}'[/green]")

                # Try the best locator first
                if await self._check_element_exists(best_locator['strategy'], best_locator['value']):
                    console.print(f"[green]✅ Element verified using {best_locator['strategy']}[/green]")

                    # Add visual highlight for assertion
                    await self._add_visual_highlight_for_assertion(best_locator['strategy'], best_locator['value'])

                    return {"success": True}

            # Fallback to original methods
            console.print("[yellow]⚠️ Analysis locator failed, trying fallback methods...[/yellow]")

            # Try multiple locator strategies
            strategies = [
                ("text", element_text),
                ("description", element_text),
                ("xpath", f"//*[@text='{element_text}']"),
                ("xpath", f"//*[@content-desc='{element_text}']")
            ]

            for strategy, value in strategies:
                if await self._check_element_exists(strategy, value):
                    console.print(f"[green]✅ Element verified using {strategy}[/green]")

                    # Add visual highlight for assertion
                    await self._add_visual_highlight_for_assertion(strategy, value)

                    return {"success": True}

            return {"success": False, "error": f"Element '{element_text}' not visible"}

        except Exception as e:
            return {"success": False, "error": str(e)}

    def _get_best_locator_for_element(self, element_text: str, analysis_data: Optional[Dict[str, Any]]) -> Optional[Dict[str, str]]:
        """Get the best locator strategy for an element from analysis data"""
        try:
            if not analysis_data or not element_text:
                return None

            element_locators = analysis_data.get('element_locators', {})
            console.print(f"[cyan]📊 Available element locators: {len(element_locators)}[/cyan]")

            # Debug: Check if there's a problematic 'e' key
            if 'e' in element_locators:
                console.print(f"[red]⚠️ Found problematic 'e' key in element_locators: {element_locators['e']}[/red]")

            # Debug: Show some sample keys
            sample_keys = list(element_locators.keys())[:5]
            console.print(f"[cyan]📋 Sample keys: {sample_keys}[/cyan]")

            # Look for exact match first (but skip single-character matches to avoid false positives)
            if element_text in element_locators and len(element_text) > 2:
                locator_info = element_locators[element_text]
                console.print(f"[green]✅ Found exact match for '{element_text}'[/green]")
                console.print(f"[cyan]📋 Locator info: {locator_info}[/cyan]")

                # Priority order: accessibility_id > resource_id > content_desc > xpath > class_name
                if locator_info.get('accessibility_id'):
                    return {
                        'strategy': 'description',
                        'value': locator_info['accessibility_id']
                    }
                elif locator_info.get('resource_id'):
                    return {
                        'strategy': 'resource_id',
                        'value': locator_info['resource_id']
                    }
                elif locator_info.get('content_desc'):
                    return {
                        'strategy': 'description',
                        'value': locator_info['content_desc']
                    }
                elif locator_info.get('xpath') and not locator_info['xpath'].startswith('//*'):
                    return {
                        'strategy': 'xpath',
                        'value': locator_info['xpath']
                    }
                elif locator_info.get('class_name'):
                    return {
                        'strategy': 'class_name',
                        'value': locator_info['class_name']
                    }

            # Look for partial matches in element text and content descriptions
            for text_key, locator_info in element_locators.items():
                # Check if element_text matches any part of the mapping key or content_desc
                content_desc = locator_info.get('content_desc', '') or ''
                text_value = locator_info.get('text', '') or ''
                accessibility_id = locator_info.get('accessibility_id', '') or ''

                # Enhanced partial matching - prioritize starts-with matches for better accuracy
                # Only match if the element_text is substantial (more than 2 characters) to avoid false positives
                if len(element_text) > 2 and (
                    content_desc.lower().startswith(element_text.lower()) or
                    text_value.lower().startswith(element_text.lower()) or
                    accessibility_id.lower().startswith(element_text.lower()) or
                    text_key.lower().startswith(element_text.lower()) or
                    (len(element_text) > 5 and element_text.lower() in content_desc.lower()) or
                    (len(element_text) > 5 and element_text.lower() in text_value.lower()) or
                    (len(element_text) > 5 and element_text.lower() in text_key.lower())):

                    console.print(f"[yellow]🔍 Found partial match in element_locators:[/yellow]")
                    console.print(f"[yellow]   Key: '{text_key[:50]}...'[/yellow]")
                    console.print(f"[yellow]   Content: '{content_desc[:50]}...'[/yellow]")

                    # Skip single-character matches to avoid false positives
                    if len(text_key) <= 2 and len(content_desc) <= 2:
                        console.print(f"[red]⚠️ Skipping single-character match to avoid false positive[/red]")
                        continue

                    if locator_info.get('accessibility_id'):
                        return {
                            'strategy': 'description',
                            'value': locator_info['accessibility_id']
                        }
                    elif locator_info.get('resource_id'):
                        return {
                            'strategy': 'resource_id',
                            'value': locator_info['resource_id']
                        }
                    elif locator_info.get('content_desc'):
                        return {
                            'strategy': 'description',
                            'value': locator_info['content_desc']
                        }

            # If element not found in primary analysis file, search other files
            if hasattr(self, 'all_analysis_files'):
                console.print(f"[yellow]🔍 Searching other analysis files for '{element_text}'...[/yellow]")
                for file_path in self.all_analysis_files:
                    if file_path == getattr(self, 'current_analysis_file', ''):
                        continue  # Skip the current file as it was already searched

                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)

                        # Try both structures
                        elements = data.get('elements', [])
                        if not elements:
                            elements = data.get('analysis_data', [])

                        # Search for the element in this file with improved partial matching
                        for element in elements:
                            content_desc = element.get('content_desc', '') or ''
                            text = element.get('text', '') or ''
                            accessibility_id = element.get('accessibility_id', '') or ''

                            # Enhanced partial matching - prioritize starts-with for better accuracy
                            if (content_desc.lower().startswith(element_text.lower()) or
                                text.lower().startswith(element_text.lower()) or
                                accessibility_id.lower().startswith(element_text.lower())):

                                console.print(f"[green]🎯 Found '{element_text}' in {os.path.basename(file_path)}[/green]")
                                console.print(f"[cyan]📝 Full content: '{content_desc[:100]}...'[/cyan]")

                                # Return the best locator for this element
                                if accessibility_id:
                                    return {
                                        'strategy': 'description',
                                        'value': accessibility_id
                                    }
                                elif content_desc:
                                    return {
                                        'strategy': 'description',
                                        'value': content_desc
                                    }
                                elif element.get('resource_id'):
                                    return {
                                        'strategy': 'resource_id',
                                        'value': element['resource_id']
                                    }
                                elif text:
                                    return {
                                        'strategy': 'text',
                                        'value': text
                                    }

                    except (json.JSONDecodeError, Exception):
                        continue

            return None

        except Exception as e:
            logger.debug(f"Failed to get best locator for element '{element_text}': {e}")
            return None

    async def _try_locator_strategy(self, strategy: str, value: str) -> bool:
        """Try a specific locator strategy to click an element"""
        try:
            if not self.device:
                logger.error("Device not initialized")
                return False

            # Add visual feedback for non-headless experience
            console.print(f"[blue]🔍 Trying {strategy} locator: '{value}'[/blue]")

            if strategy == "text":
                if self.device(text=value).exists:
                    console.print(f"[green]✅ Found element with text: '{value}'[/green]")

                    # Add visual highlight before clicking - pass actual element and text
                    element = self.device(text=value)
                    await self._add_visual_highlight(element, "CLICK", value)

                    element.click()
                    await asyncio.sleep(self.highlight_duration)  # Extended visual delay
                    return True
            elif strategy == "description":
                if self.device(description=value).exists:
                    console.print(f"[green]✅ Found element with description: '{value}'[/green]")

                    # Add visual highlight before clicking - pass actual element and text
                    element = self.device(description=value)
                    await self._add_visual_highlight(element, "CLICK", value)

                    element.click()
                    await asyncio.sleep(self.highlight_duration)  # Extended visual delay
                    return True
            elif strategy == "resource_id":
                if self.device(resourceId=value).exists:
                    console.print(f"[green]✅ Found element with resource-id: '{value}'[/green]")

                    # Add visual highlight before clicking
                    element = self.device(resourceId=value)
                    await self._add_visual_highlight(element, "CLICK", value)

                    element.click()
                    await asyncio.sleep(self.highlight_duration)  # Extended visual delay
                    return True
            elif strategy == "class_name":
                if self.device(className=value).exists:
                    console.print(f"[green]✅ Found element with class: '{value}'[/green]")

                    # Add visual highlight before clicking
                    element = self.device(className=value)
                    await self._add_visual_highlight(element, "CLICK", value)

                    element.click()
                    await asyncio.sleep(self.highlight_duration)  # Extended visual delay
                    return True
            elif strategy == "xpath":
                # For xpath, we'll use text-based fallback
                if value.startswith("//*[@text="):
                    text_value = value.split("'")[1]
                    if self.device(text=text_value).exists:
                        console.print(f"[green]✅ Found element with xpath text: '{text_value}'[/green]")

                        # Add visual highlight before clicking
                        element = self.device(text=text_value)
                        await self._add_visual_highlight(element, "CLICK", text_value)

                        element.click()
                        await asyncio.sleep(self.highlight_duration)  # Extended visual delay
                        return True
                elif value.startswith("//*[@content-desc="):
                    desc_value = value.split("'")[1]
                    if self.device(description=desc_value).exists:
                        console.print(f"[green]✅ Found element with xpath description: '{desc_value}'[/green]")

                        # Add visual highlight before clicking
                        element = self.device(description=desc_value)
                        await self._add_visual_highlight(element, "CLICK", desc_value)

                        element.click()
                        await asyncio.sleep(self.highlight_duration)  # Extended visual delay
                        return True

            console.print(f"[red]❌ Element not found with {strategy}: '{value}'[/red]")
            return False

        except Exception as e:
            logger.debug(f"Locator strategy {strategy}='{value}' failed: {e}")
            console.print(f"[red]❌ Error with {strategy} locator: {e}[/red]")
            return False

    async def _try_text_input_strategy(self, strategy: str, value: str, text: str) -> bool:
        """Try a specific locator strategy to enter text in an element with safeguards"""
        try:
            if not self.device:
                logger.error("Device not initialized")
                return False

            # Safeguard: Don't enter text if it's the same as what's already there
            element = None

            if strategy == "text":
                if self.device(text=value).exists:
                    element = self.device(text=value)
            elif strategy == "description":
                if self.device(description=value).exists:
                    element = self.device(description=value)
            elif strategy == "resource_id":
                if self.device(resourceId=value).exists:
                    element = self.device(resourceId=value)
            elif strategy == "class_name":
                if self.device(className=value).exists:
                    element = self.device(className=value)
            elif strategy == "xpath":
                # For xpath, we'll use text-based fallback
                if value.startswith("//*[@text="):
                    text_value = value.split("'")[1]
                    if self.device(text=text_value).exists:
                        element = self.device(text=text_value)

            if element:
                # Check current text to avoid infinite loops
                try:
                    current_text = element.get_text() if hasattr(element, 'get_text') else ""
                    if current_text == text:
                        console.print(f"[yellow]⚠️ Text '{text}' already present, skipping input[/yellow]")
                        return True
                except:
                    pass

                # Add visual highlight for input action
                await self._add_visual_highlight(element, "INPUT", text)

                # Clear field first, then set text
                element.clear_text()
                await asyncio.sleep(0.5)  # Brief pause
                element.set_text(text)

                # Extended visual delay
                await asyncio.sleep(self.highlight_duration)
                return True

            return False

        except Exception as e:
            logger.debug(f"Text input strategy {strategy}='{value}' failed: {e}")
            return False

    async def _check_element_exists(self, strategy: str, value: str) -> bool:
        """Check if an element exists using a specific locator strategy"""
        try:
            if strategy == "text":
                return self.device(text=value).exists
            elif strategy == "description":
                return self.device(description=value).exists
            elif strategy == "resource_id":
                return self.device(resourceId=value).exists
            elif strategy == "class_name":
                return self.device(className=value).exists
            elif strategy == "xpath":
                # For xpath, we'll use text-based fallback
                if value.startswith("//*[@text="):
                    text_value = value.split("'")[1]
                    return self.device(text=text_value).exists
                elif value.startswith("//*[@content-desc="):
                    desc_value = value.split("'")[1]
                    return self.device(description=desc_value).exists

            return False

        except Exception as e:
            logger.debug(f"Element existence check {strategy}='{value}' failed: {e}")
            return False

    async def _handle_app_switching(self, target_package: str) -> bool:
        """Handle app switching like ai-analysis (use app switching instead of back button)"""
        try:
            if not self.device:
                return False

            # Check current app
            current_app = self.device.app_current()
            current_package = current_app.get("package", "") if current_app else ""

            # If we're not in the target app, switch to it
            if target_package not in current_package:
                console.print(f"[yellow]🔄 Switching from {current_package} to {target_package}[/yellow]")
                self.device.app_start(target_package)
                await asyncio.sleep(3)  # Wait for app to load

                # Verify switch was successful
                new_app = self.device.app_current()
                new_package = new_app.get("package", "") if new_app else ""

                if target_package in new_package:
                    console.print(f"[green]✅ Successfully switched to {target_package}[/green]")
                    return True
                else:
                    console.print(f"[red]❌ Failed to switch to {target_package}[/red]")
                    return False

            return True

        except Exception as e:
            logger.error(f"App switching failed: {e}")
            return False

    def _validate_device(self) -> bool:
        """Validate device is available and responsive"""
        try:
            if not self.device:
                console.print("[red]❌ Device not initialized[/red]")
                return False

            # Test device responsiveness
            try:
                self.device.info
                return True
            except Exception as e:
                console.print(f"[red]❌ Device not responsive: {e}[/red]")
                return False

        except Exception as e:
            logger.error(f"Device validation failed: {e}")
            return False

    async def _show_ai_tooltip(self, action_type: str, element_text: str = "", additional_info: str = "") -> None:
        """Display AI tooltip showing what the AI is planning to do"""
        if not self.show_ai_tooltips:
            return

        try:
            # Create tooltip content based on action type
            tooltip_content = self._generate_tooltip_content(action_type, element_text, additional_info)

            # Create a styled panel for the tooltip
            tooltip_panel = Panel(
                tooltip_content,
                title="🤖 AI Action Plan",
                title_align="left",
                border_style="cyan",
                padding=(0, 1),
                expand=False
            )

            # Display the tooltip
            console.print(tooltip_panel)

            # Wait for the specified duration
            await asyncio.sleep(self.tooltip_duration)

        except Exception as e:
            logger.debug(f"Tooltip display error: {e}")

    def _generate_tooltip_content(self, action_type: str, element_text: str = "", additional_info: str = "") -> Text:
        """Generate tooltip content based on action type"""
        text = Text()

        if action_type == "LAUNCH_APP":
            text.append("🚀 ", style="bold yellow")
            text.append("Launching mobile application\n", style="bold white")
            text.append("• Starting the app and waiting for it to load\n", style="white")
            text.append("• Checking if app is already running\n", style="white")
            text.append("• Preparing for test execution", style="white")

        elif action_type == "TAP_ELEMENT":
            text.append("👆 ", style="bold green")
            text.append(f"Tapping on element: '{element_text}'\n", style="bold white")
            text.append("• Locating element using multiple strategies\n", style="white")
            text.append("• Using analysis data for precise targeting\n", style="white")
            text.append("• Adding visual highlight before tapping\n", style="white")
            if additional_info:
                text.append(f"• Strategy: {additional_info}", style="cyan")

        elif action_type == "ENTER_TEXT":
            text.append("⌨️ ", style="bold blue")
            text.append(f"Entering text in element: '{element_text}'\n", style="bold white")
            text.append("• Finding the input field\n", style="white")
            text.append("• Clearing existing content if needed\n", style="white")
            text.append("• Typing the specified text\n", style="white")
            if additional_info:
                text.append(f"• Text to enter: '{additional_info}'", style="yellow")

        elif action_type == "VERIFY_ELEMENT":
            text.append("🔍 ", style="bold magenta")
            text.append(f"Verifying element exists: '{element_text}'\n", style="bold white")
            text.append("• Searching for element on current screen\n", style="white")
            text.append("• Using multiple locator strategies\n", style="white")
            text.append("• Confirming element visibility", style="white")

        elif action_type == "SCROLL":
            text.append("📜 ", style="bold cyan")
            text.append(f"Scrolling {element_text or 'down'}\n", style="bold white")
            text.append("• Performing scroll gesture\n", style="white")
            text.append("• Looking for new elements after scroll\n", style="white")
            text.append("• Updating screen state", style="white")

        elif action_type == "NAVIGATE_BACK":
            text.append("⬅️ ", style="bold red")
            text.append("Navigating back\n", style="bold white")
            text.append("• Pressing device back button\n", style="white")
            text.append("• Returning to previous screen\n", style="white")
            text.append("• Updating navigation state", style="white")

        elif action_type == "WAIT":
            text.append("⏳ ", style="bold yellow")
            text.append("Waiting for UI to stabilize\n", style="bold white")
            text.append("• Allowing time for animations\n", style="white")
            text.append("• Ensuring elements are ready\n", style="white")
            text.append("• Preparing for next action", style="white")

        elif action_type == "ANALYZE_STEP":
            text.append("🧠 ", style="bold purple")
            text.append(f"Analyzing step: '{element_text}'\n", style="bold white")
            text.append("• Parsing Gherkin step syntax\n", style="white")
            text.append("• Determining required action\n", style="white")
            text.append("• Planning execution strategy\n", style="white")
            if additional_info:
                text.append(f"• Action type: {additional_info}", style="cyan")

        else:
            text.append("🤖 ", style="bold white")
            text.append(f"Performing action: {action_type}\n", style="bold white")
            text.append("• AI is processing this step\n", style="white")
            text.append("• Determining best approach\n", style="white")
            text.append("• Executing with precision", style="white")

        return text
