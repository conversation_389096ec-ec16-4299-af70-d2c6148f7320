"""
Gherkin Generator - Generate BDD test scenarios from UI analysis
"""

import os
import glob
import json
from typing import Dict, List, Any, Optional, Tuple
import pandas as pd
from rich.console import Console
from loguru import logger
from datetime import datetime
import re

from utils.config import Config

console = Console()


class GherkinGenerator:
    """Generate Gherkin test scenarios from mobile UI analysis"""

    def __init__(self):
        self.config = Config()
        self.is_initialized = False
        self.analysis_data = None
        self.generated_scenarios = set()  # Track generated scenarios to avoid duplicates
        self.supported_steps = self._initialize_supported_steps()
        self.self_learning_agent = None  # Will be injected during initialization
        self.ai_engine = None  # Will be injected during initialization for optimized AI calls
        self.app_context = {}  # Cache for application context from RAG
        self._cached_package_name = None  # Cache the extracted package name
        self._cached_package_abbreviation = None  # Cache the generated abbreviation

    def _initialize_supported_steps(self) -> Dict[str, Dict[str, Any]]:
        """Initialize supported step patterns for mobile-test execution"""
        return {
            "launch_app": {
                "patterns": [
                    "When I launch the mobile application",
                    "Given I launch the application",
                    "When I start the app"
                ],
                "description": "Launch the mobile application"
            },
            "tap_element": {
                "patterns": [
                    'When I tap on the "{element}" element',
                    'When I click on the "{element}" button',
                    'When I tap "{element}"',
                    'When I click "{element}"'
                ],
                "description": "Tap on a UI element"
            },
            "enter_text": {
                "patterns": [
                    'When I enter "{text}" in the "{element}" field',
                    'When I enter "{text}" in "{element}"',
                    'When I type "{text}" in the "{element}" input'
                ],
                "description": "Enter text in an input field"
            },
            "scroll": {
                "patterns": [
                    "When I scroll down",
                    "When I scroll up",
                    "When I scroll down on the screen",
                    "When I scroll up on the screen"
                ],
                "description": "Scroll the screen"
            },
            "verify_element": {
                "patterns": [
                    'Then I should see the "{element}" element',
                    'Then I should see "{element}"',
                    'And I should see the "{element}" interface',
                    'Then the "{element}" should be visible'
                ],
                "description": "Verify element is visible"
            },
            "verify_screen": {
                "patterns": [
                    "Then I should see the {screen} screen",
                    "Then I should be on the {screen} page",
                    "And the {screen} interface should be displayed"
                ],
                "description": "Verify screen/page is displayed"
            },
            "verify_response": {
                "patterns": [
                    "Then the application should respond to the tap",
                    "Then the interface should remain responsive",
                    "And the application should not crash"
                ],
                "description": "Verify application responsiveness"
            }
        }

    async def initialize(self, self_learning_agent=None, ai_engine=None):
        """Initialize the Gherkin generator with dependencies"""
        try:
            self.self_learning_agent = self_learning_agent
            self.ai_engine = ai_engine
            self.is_initialized = True

            # Initialize professional patterns for better scenario generation
            self.professional_patterns = {
                "setup_authentication": [
                    "Given I launch the mobile application",
                    "Given I am on the main screen"
                ],
                "navigate_to_screen": [
                    "When I navigate to the {screen_name} screen",
                    "When I access the {screen_name} feature"
                ],
                "verify_response": [
                    "Then the application should respond appropriately",
                    "Then the interface should be responsive"
                ],
                "validate_element": [
                    "Then I should see the {element} element",
                    "And the {element} should be visible"
                ],
                "assert_match": [
                    "Then the {field} should match {expected}",
                    "And the {field} should be {expected}"
                ]
            }

            console.print("[green]Gherkin Generator initialized successfully![/green]")

        except Exception as e:
            logger.error(f"Failed to initialize Gherkin Generator: {e}")
            raise

    async def _generate_optimized_ai_response(self, prompt: str, intent: str = "gherkin_generation") -> str:
        """Generate AI response using optimized engine with performance settings and caching"""
        try:
            # Check cache first if AI engine is available
            if self.ai_engine and hasattr(self.ai_engine, 'performance_optimizer'):
                cached_response = self.ai_engine.performance_optimizer.get_cached_response(prompt, {"intent": intent}, custom_ttl=600)  # 10 minute cache for Gherkin
                if cached_response:
                    console.print(f"[green]🚀 Using cached Gherkin response ({len(cached_response)} characters)[/green]")
                    return cached_response

            # Use the optimized AI engine if available
            if self.ai_engine and hasattr(self.ai_engine, 'generate_response'):
                console.print(f"[yellow]⚡ Using optimized AI engine for Gherkin generation...[/yellow]")
                response = await self.ai_engine.generate_response(prompt, intent)

                # Check if response is meaningful (not just timeout error)
                if response and len(response.strip()) > 20 and not response.startswith("Error"):
                    console.print(f"[green]✅ Optimized AI response received ({len(response)} characters)[/green]")
                    return response
                else:
                    console.print(f"[yellow]⚠️ AI response too short or error, using template fallback[/yellow]")
                    return self._generate_template_fallback(prompt)

            # Fallback to self_learning_agent content analyzer (slower)
            elif self.self_learning_agent and hasattr(self.self_learning_agent, 'content_analyzer'):
                console.print(f"[yellow]⚠️ Using fallback AI method (slower)...[/yellow]")
                response = await self.self_learning_agent.content_analyzer._generate_ai_summary(prompt)

                # Check fallback response quality
                if response and len(response.strip()) > 20:
                    console.print(f"[green]✅ Fallback AI response received ({len(response)} characters)[/green]")
                    return response
                else:
                    console.print(f"[yellow]⚠️ Fallback AI failed, using template[/yellow]")
                    return self._generate_template_fallback(prompt)

            else:
                logger.warning("No AI engine available for Gherkin generation, using template")
                return self._generate_template_fallback(prompt)

        except Exception as e:
            logger.error(f"Optimized AI response generation failed: {e}")
            console.print(f"[yellow]⚠️ AI generation failed, using template fallback[/yellow]")
            return self._generate_template_fallback(prompt)

    def _generate_template_fallback(self, prompt: str) -> str:
        """Generate a basic Gherkin scenario template when AI fails"""
        # Extract feature name from prompt if possible
        feature_name = "Feature"
        if "Feature:" in prompt:
            try:
                feature_name = prompt.split("Feature:")[1].split("\n")[0].strip()
            except:
                pass

        # Generate basic template
        template = f"""Feature: {feature_name}
Scenario: Validate text display
  Given I launch the mobile application
  When I navigate to the main page
  Then I should see the interface is loaded
  And the application should be responsive"""

        console.print(f"[blue]📝 Generated template fallback ({len(template)} characters)[/blue]")
        return template

    async def generate_scenarios(self, custom_instructions: str = "") -> Dict[str, Any]:
        """Main method to generate Gherkin scenarios"""
        try:
            if not self.is_initialized:
                return {
                    "success": False,
                    "error": "Gherkin generator not initialized. Call initialize() first."
                }

            if custom_instructions:
                # Generate from custom instructions
                return await self._generate_from_custom_instructions(custom_instructions)
            else:
                # Generate from analysis data
                return await self._generate_from_analysis()

        except Exception as e:
            logger.error(f"Scenario generation failed: {e}", exc_info=True)
            return {"success": False, "error": str(e)}

    async def generate_scenarios_from_excel(self, excel_file: str) -> Dict[str, Any]:
        """Generate Gherkin scenarios from Excel file containing acceptance criteria"""
        try:
            if not self.is_initialized:
                return {
                    "success": False,
                    "error": "Gherkin generator not initialized. Call initialize() first."
                }

            console.print(f"[cyan]Reading Excel file: {excel_file}[/cyan]")

            # Read the Excel file
            try:
                df = pd.read_excel(excel_file, engine='openpyxl')
                console.print(f"[green]Successfully read Excel file with {len(df)} rows[/green]")
                console.print(f"[cyan]Columns found: {list(df.columns)}[/cyan]")
            except Exception as e:
                return {
                    "success": False,
                    "error": f"Failed to read Excel file: {e}"
                }

            # Check if this is an acceptance criteria file
            if 'Acceptance_Criteria' in df.columns and 'Feature' in df.columns:
                return await self._generate_from_acceptance_criteria(df)
            else:
                # Fallback to analysis-based generation
                console.print("[yellow]Excel file doesn't contain acceptance criteria. Treating as analysis data.[/yellow]")
                return await self._generate_from_analysis_excel(excel_file)

        except Exception as e:
            logger.error(f"Excel-based generation failed: {e}", exc_info=True)
            return {"success": False, "error": str(e)}

    async def _generate_from_acceptance_criteria(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Generate Gherkin scenarios from acceptance criteria DataFrame with optimizations"""
        try:
            generated_files = []
            total_scenarios = 0

            console.print(f"[cyan]Processing {len(df)} acceptance criteria entries...[/cyan]")

            # Batch process all features in a single AI call for better performance
            if len(df) > 1:
                console.print(f"[yellow]Using batch processing for {len(df)} features to improve speed...[/yellow]")
                return await self._batch_generate_from_criteria(df)

            # Single feature processing (fallback)
            for row_num, (_, row) in enumerate(df.iterrows()):
                feature_name = str(row.get('Feature', f'Feature_{row_num + 1}')).strip()
                acceptance_criteria = str(row.get('Acceptance_Criteria', '')).strip()

                if not acceptance_criteria or acceptance_criteria.lower() in ['nan', 'null', '']:
                    console.print(f"[yellow]Skipping row {row_num + 1}: No acceptance criteria found[/yellow]")
                    continue

                console.print(f"[cyan]Generating scenarios for feature: {feature_name}[/cyan]")

                # Generate scenarios from acceptance criteria
                result = await self._generate_feature_scenarios_from_criteria(feature_name, acceptance_criteria)

                if result.get("success") and result.get("scenarios"):
                    scenarios = result["scenarios"]

                    # Save feature file without timestamp for Excel-based generation
                    file_path = await self._save_feature_file_without_timestamp(feature_name, scenarios)
                    generated_files.append(file_path)
                    total_scenarios += len(scenarios)

                    # Update testsuite Excel file
                    await self._update_testsuite_excel(feature_name, scenarios, file_path)

                    console.print(f"[green]Generated {len(scenarios)} scenarios for {feature_name}[/green]")
                else:
                    console.print(f"[red]Failed to generate scenarios for {feature_name}: {result.get('error', 'Unknown error')}[/red]")

            if generated_files:
                console.print(f"[green]Successfully generated {len(generated_files)} feature files with {total_scenarios} scenarios![/green]")
                return {
                    "success": True,
                    "message": f"Generated {len(generated_files)} feature files from acceptance criteria",
                    "data": {
                        "files_generated": len(generated_files),
                        "feature_files": generated_files,
                        "scenarios_generated": total_scenarios
                    }
                }
            else:
                return {
                    "success": False,
                    "error": "No scenarios could be generated from the acceptance criteria"
                }

        except Exception as e:
            logger.error(f"Failed to generate from acceptance criteria: {e}", exc_info=True)
            return {"success": False, "error": str(e)}

    async def _generate_from_analysis_excel(self, excel_file: str) -> Dict[str, Any]:
        """Generate scenarios from analysis Excel file (fallback method)"""
        try:
            # This is the original logic that was at the beginning of the file
            # Load Excel file as analysis data
            try:
                self.analysis_data = pd.read_excel(excel_file, engine='openpyxl')
                console.print(f"[cyan]Loaded analysis data from Excel: {len(self.analysis_data)} elements[/cyan]")
            except Exception as e:
                return {
                    "success": False,
                    "error": f"Failed to read Excel file as analysis data: {e}"
                }

            # Group data by features
            feature_groups = self._group_by_features()

            generated_files = []
            total_scenarios = 0

            for feature_name, feature_data in feature_groups.items():
                scenarios = await self._generate_feature_scenarios(feature_name, feature_data)

                if scenarios:
                    # Create feature file path
                    feature_file_path = os.path.join(
                        self.config.get("FEATURE_OUTPUT_PATH"),
                        f"{feature_name.replace(' ', '_').lower()}.feature"
                    )

                    # Write feature file
                    with open(feature_file_path, "w", encoding="utf-8") as f:
                        f.write(f"Feature: {feature_name}\n\n")
                        for scenario in scenarios:
                            f.write(f"{scenario}\n\n")

                    generated_files.append(feature_file_path)
                    total_scenarios += len(scenarios)

                    # Update testsuite Excel file
                    await self._update_testsuite_excel(feature_name, scenarios, feature_file_path)

                    console.print(f"[green]Generated feature file: {feature_file_path}[/green]")
                    console.print(f"[green]Updated testsuite.xlsx with scenarios for feature: {feature_name}[/green]")

            console.print(
                f"[green]Generated {len(generated_files)} "
                "Gherkin feature files from Excel![/green]"
            )

            return {
                "success": True,
                "message": (
                    f"Generated {len(generated_files)} "
                    "Gherkin feature files from Excel"
                ),
                "data": {
                    "files_generated": len(generated_files),
                    "feature_files": generated_files,
                    "features_analyzed": len(feature_groups),
                    "scenarios_generated": total_scenarios
                }
            }

        except Exception as e:
            logger.error(f"Gherkin generation from Excel failed: {e}", exc_info=True)
            return {"success": False, "error": str(e)}

    async def _batch_generate_from_criteria(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Batch process multiple features in a single AI call for better performance"""
        try:
            console.print(f"[cyan]🚀 Batch processing {len(df)} features for faster generation...[/cyan]")

            # Prepare batch data
            batch_data = []
            for _, row in df.iterrows():
                feature_name = str(row.get('Feature', '')).strip()
                acceptance_criteria = str(row.get('Acceptance_Criteria', '')).strip()

                if feature_name and acceptance_criteria and acceptance_criteria.lower() not in ['nan', 'null', '']:
                    batch_data.append({
                        'feature': feature_name,
                        'criteria': acceptance_criteria
                    })

            if not batch_data:
                return {"success": False, "error": "No valid acceptance criteria found"}

            # Create optimized batch prompt
            batch_prompt = self._create_batch_prompt(batch_data)

            # Single AI call for all features using optimized method
            console.print(f"[yellow]⚡ Making optimized AI call for {len(batch_data)} features...[/yellow]")
            response = await self._generate_optimized_ai_response(batch_prompt, "gherkin_generation")

            # Process batch response
            if response and response.strip():
                return await self._process_batch_response(response, batch_data)
            else:
                # Fallback to individual processing
                console.print(f"[yellow]⚠️ Batch processing failed, falling back to individual processing...[/yellow]")
                return await self._fallback_individual_processing(batch_data)

        except Exception as e:
            logger.error(f"Batch processing failed: {e}", exc_info=True)
            return {"success": False, "error": str(e)}

    def _create_batch_prompt(self, batch_data: List[Dict]) -> str:
        """Create optimized prompt for batch processing multiple features"""
        # Create a much simpler, more direct prompt for better AI response
        prompt = f"""Create Gherkin test scenarios. Return ONLY the scenarios in the exact format shown.

"""

        for i, data in enumerate(batch_data, 1):
            prompt += f"""Feature: {data['feature']}
Criteria: {data['criteria']}

"""

        prompt += """Return exactly this format for each feature:

Feature: [feature_name]
Scenario: Validate text display
  Given I launch the mobile application
  When I navigate to the main page
  Then I should see "[text]" element

Start generating:"""

        return prompt

    async def _process_batch_response(self, response: str, batch_data: List[Dict]) -> Dict[str, Any]:
        """Process the batch AI response and create feature files"""
        try:
            generated_files = []
            total_scenarios = 0

            # Split response by features
            feature_sections = self._split_batch_response(response, batch_data)

            for feature_data in batch_data:
                feature_name = feature_data['feature']

                # Find scenarios for this feature
                scenarios = feature_sections.get(feature_name, [])

                if not scenarios:
                    # Generate fallback scenario
                    scenarios = self._generate_enhanced_scenarios_from_criteria(
                        feature_name,
                        self._parse_acceptance_criteria(feature_data['criteria'])
                    )

                if scenarios:
                    # Save feature file without timestamp for Excel-based generation
                    file_path = await self._save_feature_file_without_timestamp(feature_name, scenarios)
                    generated_files.append(file_path)
                    total_scenarios += len(scenarios)

                    # Update testsuite Excel file
                    await self._update_testsuite_excel(feature_name, scenarios, file_path)

                    console.print(f"[green]✅ Generated {len(scenarios)} scenarios for {feature_name}[/green]")

            return {
                "success": True,
                "message": f"Generated {len(generated_files)} feature files from acceptance criteria",
                "data": {
                    "files_generated": len(generated_files),
                    "feature_files": generated_files,
                    "scenarios_generated": total_scenarios
                }
            }

        except Exception as e:
            logger.error(f"Failed to process batch response: {e}", exc_info=True)
            return {"success": False, "error": str(e)}

    def _split_batch_response(self, response: str, batch_data: List[Dict]) -> Dict[str, List[str]]:
        """Split batch response into individual feature scenarios"""
        try:
            feature_scenarios = {}
            current_feature = None
            current_scenario = []

            lines = response.split('\n')

            for line in lines:
                line = line.strip()

                # Check for feature declaration
                if line.startswith('Feature:'):
                    # Save previous feature scenarios
                    if current_feature and current_scenario:
                        if current_feature not in feature_scenarios:
                            feature_scenarios[current_feature] = []
                        feature_scenarios[current_feature].append('\n'.join(current_scenario))

                    # Start new feature
                    feature_name = line.replace('Feature:', '').strip()
                    current_feature = self._find_matching_feature(feature_name, batch_data)
                    current_scenario = []

                # Check for scenario start
                elif line.startswith('Scenario:') and current_feature:
                    # Save previous scenario
                    if current_scenario:
                        if current_feature not in feature_scenarios:
                            feature_scenarios[current_feature] = []
                        feature_scenarios[current_feature].append('\n'.join(current_scenario))

                    # Start new scenario
                    current_scenario = [line]

                # Add line to current scenario
                elif current_scenario and line:
                    current_scenario.append(line)

                # Handle separator
                elif line == '---':
                    if current_feature and current_scenario:
                        if current_feature not in feature_scenarios:
                            feature_scenarios[current_feature] = []
                        feature_scenarios[current_feature].append('\n'.join(current_scenario))
                        current_scenario = []

            # Save last scenario
            if current_feature and current_scenario:
                if current_feature not in feature_scenarios:
                    feature_scenarios[current_feature] = []
                feature_scenarios[current_feature].append('\n'.join(current_scenario))

            return feature_scenarios

        except Exception as e:
            logger.error(f"Failed to split batch response: {e}")
            return {}

    def _find_matching_feature(self, feature_name: str, batch_data: List[Dict]) -> str:
        """Find the matching feature name from batch data"""
        feature_name_lower = feature_name.lower()
        for data in batch_data:
            if data['feature'].lower() == feature_name_lower:
                return data['feature']
        return feature_name

    async def _fallback_individual_processing(self, batch_data: List[Dict]) -> Dict[str, Any]:
        """Fallback to individual processing if batch fails"""
        try:
            generated_files = []
            total_scenarios = 0

            for data in batch_data:
                feature_name = data['feature']
                acceptance_criteria = data['criteria']

                console.print(f"[cyan]Processing {feature_name} individually...[/cyan]")

                # Generate enhanced scenarios without AI call
                parsed_criteria = self._parse_acceptance_criteria(acceptance_criteria)
                scenarios = self._generate_enhanced_scenarios_from_criteria(feature_name, parsed_criteria)

                if scenarios:
                    # Save feature file without timestamp for Excel-based generation
                    file_path = await self._save_feature_file_without_timestamp(feature_name, scenarios)
                    generated_files.append(file_path)
                    total_scenarios += len(scenarios)

                    # Update testsuite Excel file
                    await self._update_testsuite_excel(feature_name, scenarios, file_path)

                    console.print(f"[green]Generated {len(scenarios)} scenarios for {feature_name}[/green]")

            return {
                "success": True,
                "message": f"Generated {len(generated_files)} feature files from acceptance criteria",
                "data": {
                    "files_generated": len(generated_files),
                    "feature_files": generated_files,
                    "scenarios_generated": total_scenarios
                }
            }

        except Exception as e:
            logger.error(f"Fallback processing failed: {e}", exc_info=True)
            return {"success": False, "error": str(e)}

    async def _generate_feature_scenarios_from_criteria(self, feature_name: str, acceptance_criteria: str) -> Dict[str, Any]:
        """Generate Gherkin scenarios from acceptance criteria with optimized processing"""
        try:
            console.print(f"[cyan]Generating scenarios for feature: {feature_name}[/cyan]")

            # Parse and understand the acceptance criteria
            parsed_criteria = self._parse_acceptance_criteria(acceptance_criteria)
            console.print(f"[cyan]Parsed {len(parsed_criteria)} acceptance criteria points[/cyan]")

            # Create optimized prompt for faster processing
            prompt = f"""Create test scenario for {feature_name}.

Criteria: {acceptance_criteria}

Return exactly:
Scenario: Validate text display
  Given I launch the mobile application
  When I navigate to the main page
  Then I should see "[text]" element

Generate now:"""

            # Try to get response from optimized AI engine
            console.print(f"[yellow]⚡ Calling optimized AI for {feature_name}...[/yellow]")
            response = await self._generate_optimized_ai_response(prompt, "gherkin_generation")

            if not response or not response.strip():
                # Generate enhanced scenarios as fallback
                scenarios = self._generate_enhanced_scenarios_from_criteria(feature_name, parsed_criteria)
                console.print(f"[yellow]⚠️ Using fallback generation for {feature_name}[/yellow]")
            else:
                # Process the response to extract scenarios
                scenarios = self._extract_scenarios_from_response(response)

                if not scenarios:
                    # Generate enhanced scenarios as fallback
                    scenarios = self._generate_enhanced_scenarios_from_criteria(feature_name, parsed_criteria)
                    console.print(f"[yellow]⚠️ Using fallback generation for {feature_name}[/yellow]")

            console.print(f"[green]✅ Generated {len(scenarios)} scenarios for {feature_name}[/green]")
            return {"success": True, "scenarios": scenarios}

        except Exception as e:
            logger.error(f"Failed to generate scenarios from criteria for feature '{feature_name}': {e}")
            return {"success": False, "error": str(e)}
            
    def _extract_scenarios_from_response(self, response: str) -> List[str]:
        """Extract individual scenarios from the LLM response"""
        scenarios = []
        current_scenario = []
        in_scenario = False
        
        for line in response.split('\n'):
            line = line.strip()
            
            # Skip empty lines and code block markers
            if not line or line.startswith('```') or line.startswith('`'):
                continue
                
            # Check for scenario start
            if line.startswith('Scenario:') or line.startswith('Scenario Outline:') or line.startswith('@') and not in_scenario:
                in_scenario = True
                current_scenario = [line]
            # Add lines to current scenario
            elif in_scenario:
                current_scenario.append(line)
                # Check for scenario end (empty line or start of new scenario)
                if (not line and len(current_scenario) > 1) or \
                   (line.startswith('Scenario:') or line.startswith('Scenario Outline:') or line.startswith('@')):
                    # Save the completed scenario
                    scenarios.append('\n'.join(current_scenario))
                    # Start a new scenario if this is the beginning of one
                    if line.startswith('Scenario:') or line.startswith('Scenario Outline:') or line.startswith('@'):
                        current_scenario = [line]
                    else:
                        in_scenario = False
                        current_scenario = []
        
        # Add the last scenario if there is one
        if current_scenario and in_scenario:
            scenarios.append('\n'.join(current_scenario))
            
        return scenarios

    def _generate_basic_scenarios_from_criteria(self, feature_name: str, acceptance_criteria: str) -> List[str]:
        """Generate basic scenarios from acceptance criteria as fallback when LLM is not available"""
        try:
            scenarios = []

            # Split acceptance criteria into individual criteria
            criteria_lines = [line.strip() for line in acceptance_criteria.split('\n') if line.strip()]

            for i, criterion in enumerate(criteria_lines):
                if criterion.startswith('-'):
                    criterion = criterion[1:].strip()

                # Generate a basic scenario for each criterion
                scenario_title = f"Verify {criterion.lower()}"
                scenario = f"""
  Scenario: {scenario_title}
    Given I launch the "Rumah Pendidikan" application
    When I navigate to the "{feature_name}" section
    Then I should see the interface
    And {criterion.lower()}
    And the application should be responsive
"""
                scenarios.append(scenario)

            # If no criteria found, generate a basic scenario
            if not scenarios:
                scenario = f"""
  Scenario: Basic functionality test for {feature_name}
    Given I launch the "Rumah Pendidikan" application
    When I navigate to the "{feature_name}" section
    Then I should see the {feature_name} interface
    And the application should be responsive
    And all elements should be visible
"""
                scenarios.append(scenario)

            return scenarios

        except Exception as e:
            logger.error(f"Failed to generate basic scenarios: {e}")
            # Return a minimal scenario as ultimate fallback
            return [f"""
  Scenario: Basic test for {feature_name}
    Given I launch the application
    When I access {feature_name}
    Then I should see the interface
"""]

    def _parse_acceptance_criteria(self, acceptance_criteria: str) -> List[str]:
        """Parse acceptance criteria text into individual criteria points"""
        try:
            criteria_points = []

            # Split by lines and process each line
            lines = acceptance_criteria.strip().split('\n')

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # Remove bullet points and dashes
                if line.startswith('-'):
                    line = line[1:].strip()
                elif line.startswith('•'):
                    line = line[1:].strip()
                elif line.startswith('*'):
                    line = line[1:].strip()

                if line:
                    criteria_points.append(line)

            return criteria_points

        except Exception as e:
            logger.error(f"Failed to parse acceptance criteria: {e}")
            return [acceptance_criteria]  # Return original as single item

    def _generate_enhanced_scenarios_from_criteria(self, feature_name: str, parsed_criteria: List[str]) -> List[str]:
        """Generate enhanced scenarios from parsed acceptance criteria as fallback when LLM is not available"""
        try:
            scenarios = []

            for i, criterion in enumerate(parsed_criteria):
                # Extract key information from the criterion
                scenario_info = self._extract_scenario_info_from_criterion(criterion)

                # Generate a professional scenario for each criterion
                scenario_title = f"Verify {scenario_info['action']}"
                scenario = f"""
  Scenario: {scenario_title}
    Given I launch the mobile application
    When I navigate to the main page
    Then I should see the interface is loaded
    And {criterion.lower()}
    And the application should be responsive
"""
                scenarios.append(scenario)

            # If no criteria found, generate a basic scenario
            if not scenarios:
                scenario = f"""
  Scenario: Basic functionality test for {feature_name}
    Given I launch the mobile application
    When I navigate to the "{feature_name}" section
    Then I should see the {feature_name} interface
    And the application should be responsive
    And all elements should be visible
"""
                scenarios.append(scenario)

            return scenarios

        except Exception as e:
            logger.error(f"Failed to generate enhanced scenarios: {e}")
            # Return a minimal scenario as ultimate fallback
            return [f"""
  Scenario: Basic test for {feature_name}
    Given I launch the application
    When I access {feature_name}
    Then I should see the interface
"""]

    def _extract_scenario_info_from_criterion(self, criterion: str) -> Dict[str, str]:
        """Extract key information from acceptance criteria for scenario generation"""
        try:
            info = {
                'action': 'functionality',
                'element': 'interface',
                'expected': 'should work correctly'
            }

            # Look for text validation patterns
            if 'text' in criterion.lower() and '"' in criterion:
                # Extract text in quotes
                import re
                text_matches = re.findall(r'"([^"]*)"', criterion)
                if text_matches:
                    info['element'] = text_matches[0]
                    info['action'] = f'text "{text_matches[0]}" is visible'
                    info['expected'] = f'should display "{text_matches[0]}"'

            # Look for validation patterns
            elif 'validate' in criterion.lower():
                info['action'] = 'validation functionality'
                info['expected'] = 'should validate correctly'

            # Look for navigation patterns
            elif 'navigate' in criterion.lower() or 'page' in criterion.lower():
                info['action'] = 'navigation functionality'
                info['expected'] = 'should navigate correctly'

            return info

        except Exception as e:
            logger.debug(f"Failed to extract scenario info: {e}")
            return {
                'action': 'functionality',
                'element': 'interface',
                'expected': 'should work correctly'
            }

    async def _get_rag_enhanced_context(self, feature_name: str, feature_data: pd.DataFrame) -> Dict[str, Any]:
        """Get RAG-enhanced context for better scenario generation"""
        try:
            enhanced_context = {}

            # If we have a self-learning agent, use it to get context
            if self.self_learning_agent and hasattr(self.self_learning_agent, 'search_knowledge'):
                try:
                    # Search for relevant context about the feature
                    search_queries = [
                        f"navigation patterns for {feature_name}",
                        f"user interactions in {feature_name}",
                        f"testing scenarios for {feature_name}",
                        f"mobile UI patterns for {feature_name}"
                    ]

                    for query in search_queries:
                        try:
                            results = await self.self_learning_agent.search_knowledge(query)
                            if results:
                                enhanced_context[query] = results
                        except Exception as e:
                            logger.debug(f"RAG search failed for query '{query}': {e}")
                            continue

                except Exception as e:
                    logger.debug(f"RAG context retrieval failed: {e}")

            # Add basic context based on feature name
            if not enhanced_context:
                enhanced_context = {
                    "basic_context": f"Mobile UI testing context for {feature_name} feature"
                }

            return enhanced_context

        except Exception as e:
            logger.debug(f"Failed to get RAG enhanced context: {e}")
            return {}

    async def _generate_from_analysis(self) -> Dict[str, Any]:
        """Generate scenarios from latest analysis file"""
        # Load latest analysis file
        analysis_file = await self._get_latest_analysis_file()
        if not analysis_file:
            return {
                "success": False,
                "error": "No analysis file found. "
                         "Please run /ai-analysis first."
            }

        # Load analysis data - check if file is actually JSON regardless of extension
        try:
            # Check if file content is JSON by reading first few characters
            with open(analysis_file, 'r', encoding='utf-8') as f:
                first_char = f.read(1)
                f.seek(0)  # Reset file pointer

                if first_char == '{':
                    # File is JSON format
                    json_data = json.load(f)

                    # Store the original JSON data for package extraction
                    self.original_json_data = json_data
                    self.analysis_file_path = analysis_file

                    # Extract elements from JSON structure
                    if 'analysis_data' in json_data:
                        # Handle live analysis JSON structure
                        self.analysis_data = pd.DataFrame(json_data['analysis_data'])
                        console.print(f"[cyan]Loaded analysis data from JSON: {len(self.analysis_data)} elements[/cyan]")
                    elif 'elements' in json_data:
                        # Handle standard JSON structure
                        self.analysis_data = pd.DataFrame(json_data['elements'])
                        console.print(f"[cyan]Loaded analysis data from JSON: {len(self.analysis_data)} elements[/cyan]")
                    else:
                        return {
                            "success": False,
                            "error": "Invalid JSON structure: no 'elements' or 'analysis_data' key found"
                        }

                    # Initialize package information once for all tickets
                    self._initialize_package_info()
                else:
                    # File is Excel format
                    raise ValueError("Not a JSON file")

        except (json.JSONDecodeError, ValueError, UnicodeDecodeError):
            # File is not JSON or has encoding issues, try Excel
            # Read Excel file - try to read from Elements sheet first
            try:
                # Try to read from Elements sheet (detailed element data) with explicit engine
                self.analysis_data = pd.read_excel(analysis_file, sheet_name='Elements', engine='openpyxl')
                console.print(f"[cyan]Loaded detailed element data from Excel 'Elements' sheet: {len(self.analysis_data)} elements[/cyan]")
            except Exception as e:
                console.print(f"[yellow]Could not read 'Elements' sheet: {e}[/yellow]")
                console.print("[yellow]Trying default sheet...[/yellow]")

                try:
                    # Fallback to default sheet with explicit engine
                    self.analysis_data = pd.read_excel(analysis_file, engine='openpyxl')
                    console.print(f"[cyan]Loaded analysis data from Excel default sheet: {len(self.analysis_data)} elements[/cyan]")
                except Exception as e2:
                    console.print(f"[red]Failed to read Excel file with openpyxl engine: {e2}[/red]")
                    console.print("[yellow]Trying with xlrd engine...[/yellow]")
                    try:
                        # Try with xlrd engine as fallback
                        self.analysis_data = pd.read_excel(analysis_file, engine='xlrd')
                        console.print(f"[cyan]Loaded analysis data from Excel with xlrd engine: {len(self.analysis_data)} elements[/cyan]")
                    except Exception as e3:
                        console.print(f"[red]Failed to read Excel file with any engine: {e3}[/red]")
                        return {
                            "success": False,
                            "error": f"Cannot read Excel file: {e3}. Please ensure the file is not corrupted or open in another application."
                        }

                # Check if this is a summary file (only Metric/Value columns)
                if list(self.analysis_data.columns) == ['Metric', 'Value']:
                    console.print("[yellow]This appears to be a summary file, not detailed element data[/yellow]")
                    console.print("[yellow]Looking for JSON files with detailed element data...[/yellow]")

                    # Try to find a JSON file instead
                    analysis_path = self.config.get("ANALYSIS_OUTPUT_PATH")
                    json_patterns = [
                        os.path.join(analysis_path, "*.json")
                    ]

                    json_files = []
                    for pattern in json_patterns:
                        files = glob.glob(pattern)
                        json_files.extend(files)

                    if json_files:
                        # Use the most recent JSON file
                        latest_json = max(json_files, key=os.path.getctime)
                        console.print(f"[cyan]Found JSON file: {os.path.basename(latest_json)}[/cyan]")

                        with open(latest_json, 'r', encoding='utf-8') as f:
                            json_data = json.load(f)

                        # Store the original JSON data for package extraction
                        self.original_json_data = json_data
                        self.analysis_file_path = latest_json

                        if 'elements' in json_data:
                            self.analysis_data = pd.DataFrame(json_data['elements'])
                            console.print(f"[cyan]Loaded detailed element data from JSON: {len(self.analysis_data)} elements[/cyan]")
                        elif 'analysis_data' in json_data:
                            self.analysis_data = pd.DataFrame(json_data['analysis_data'])
                            console.print(f"[cyan]Loaded detailed element data from JSON: {len(self.analysis_data)} elements[/cyan]")
                        else:
                            return {
                                "success": False,
                                "error": "No detailed element data found in JSON files"
                            }

                        # Initialize package information once for all tickets
                        self._initialize_package_info()
                    else:
                        return {
                            "success": False,
                            "error": "No JSON files with detailed element data found. Excel file contains only summary data."
                        }

        # Group data by features (screens)
        features = self._group_by_features()

        # Generate scenarios for each feature
        generated_files = []
        total_scenarios = 0
        for feature_name, feature_data in features.items():
            scenarios = await self._generate_feature_scenarios(
                feature_name, feature_data
            )

            if scenarios:
                file_path = await self._save_feature_file_with_timestamp(
                    feature_name, scenarios
                )
                generated_files.append(file_path)
                total_scenarios += len(scenarios)

                # Update testsuite.xlsx for analysis-based generation (no parsed instructions)
                await self._update_testsuite_excel(feature_name, scenarios, file_path)

        console.print(
            f"[green]Generated {len(generated_files)} "
            "Gherkin feature files![/green]"
        )

        return {
            "success": True,
            "message": (
                f"Generated {len(generated_files)} "
                "Gherkin feature files"
            ),
            "data": {
                "files_generated": len(generated_files),
                "feature_files": generated_files,
                "features_analyzed": len(features),
                "scenarios_generated": total_scenarios
            }
        }

    async def _generate_from_custom_instructions(self, instructions: str) -> Dict[str, Any]:
        """Generate scenarios from custom user instructions"""
        try:
            # First, load analysis data to validate elements
            console.print("[cyan]Loading analysis data for element validation...[/cyan]")
            analysis_file = await self._get_latest_analysis_file()
            if analysis_file:
                console.print(f"[cyan]Found analysis file: {analysis_file}[/cyan]")
                success = await self._load_analysis_data(analysis_file)
                if success and self.analysis_data is not None:
                    console.print(f"[green]✅ Analysis data loaded successfully: {len(self.analysis_data)} elements[/green]")
                else:
                    console.print("[red]❌ Failed to load analysis data[/red]")
            else:
                console.print("[yellow]⚠️ No analysis data found. Elements will not be validated against actual app state.[/yellow]")

            # Parse the instructions to extract test case details
            parsed_instructions = self._parse_custom_instructions(instructions)

            if not parsed_instructions:
                return {
                    "success": False,
                    "error": "Could not parse custom instructions. Please provide clear test case requirements."
                }

            # Generate scenarios based on parsed instructions
            scenarios = []
            for instruction in parsed_instructions:
                generated_scenarios = self._generate_custom_scenario(instruction)
                if generated_scenarios:
                    # The method now returns a list of scenarios
                    scenarios.extend(generated_scenarios)

            if not scenarios:
                return {
                    "success": False,
                    "error": "No valid scenarios could be generated from the instructions."
                }

            # Validate and ensure all element locators are available
            console.print(f"[yellow]🔍 Validating element locators for {len(scenarios)} scenarios...[/yellow]")
            validated_scenarios, validation_results = await self._validate_and_ensure_element_locators(scenarios)

            if not validated_scenarios:
                return {
                    "success": False,
                    "error": "No scenarios could be validated - all required element locators are missing",
                    "validation_results": validation_results
                }

            # Create feature name from instructions
            feature_name = self._extract_feature_name_from_instructions(instructions)

            # Save feature file with timestamp
            file_path = await self._save_feature_file_with_timestamp(feature_name, validated_scenarios)

            # Update testsuite.xlsx with case type information
            await self._update_testsuite_excel(feature_name, validated_scenarios, file_path, parsed_instructions)

            console.print(f"[green]Generated custom Gherkin scenarios: {file_path}[/green]")

            return {
                "success": True,
                "message": f"Generated custom Gherkin scenarios based on instructions",
                "data": {
                    "files_generated": 1,
                    "feature_files": [file_path],
                    "scenarios_generated": len(validated_scenarios),
                    "feature_name": feature_name,
                    "validation_results": validation_results
                }
            }

        except Exception as e:
            logger.error(f"Custom instruction generation failed: {e}", exc_info=True)
            return {"success": False, "error": str(e)}

    async def _get_latest_analysis_file(self) -> Optional[str]:
        """Get the latest analysis file"""
        try:
            analysis_path = self.config.get("ANALYSIS_OUTPUT_PATH")

            # First, try to find live analysis files specifically (they contain detailed data)
            live_analysis_patterns = [
                os.path.join(analysis_path, "*_live_analysis.json"),     # JSON live analysis files (detailed data)
                os.path.join(analysis_path, "*_live_analysis.xlsx"),     # Excel live analysis files (detailed data)
            ]

            live_files = []
            for pattern in live_analysis_patterns:
                files = glob.glob(pattern)
                # Filter out temporary Excel files (starting with ~$) and other temporary files
                filtered_files = [
                    f for f in files
                    if not os.path.basename(f).startswith('~$')
                    and not os.path.basename(f).startswith('.')
                    and os.path.isfile(f)
                ]
                live_files.extend(filtered_files)

            if live_files:
                # Found live analysis files - use the most recent one
                latest_live_file = max(live_files, key=os.path.getctime)

                # Check file age and warn if old
                import time
                file_age_hours = (time.time() - os.path.getctime(latest_live_file)) / 3600

                console.print(f"[cyan]Found latest live analysis file: {os.path.basename(latest_live_file)}[/cyan]")
                console.print(f"[dim]File age: {file_age_hours:.1f} hours[/dim]")

                if file_age_hours > 24:
                    console.print(f"[yellow]⚠️ Warning: Latest analysis file is {file_age_hours:.1f} hours old[/yellow]")
                    console.print("[yellow]💡 Consider running '/ai-analysis' to get fresh data[/yellow]")
                elif file_age_hours > 6:
                    console.print(f"[yellow]📅 Note: Analysis file is {file_age_hours:.1f} hours old[/yellow]")

                return latest_live_file

            # Fallback to other analysis files if no live analysis files found
            fallback_patterns = [
                os.path.join(analysis_path, "*_unified_analysis.json"),  # JSON unified analysis files
                os.path.join(analysis_path, "mobile_analysis_*.json"),   # JSON original pattern
                os.path.join(analysis_path, "com.*.json"),               # JSON package-based naming
                os.path.join(analysis_path, "*.json"),                   # Any JSON file
                os.path.join(analysis_path, "*_unified_analysis.xlsx"),  # Excel unified analysis files (summary)
                os.path.join(analysis_path, "mobile_analysis_*.xlsx"),   # Excel original pattern
                os.path.join(analysis_path, "com.*.xlsx"),               # Excel package-based naming
                os.path.join(analysis_path, "*.xlsx")                    # Any Excel file as fallback
            ]

            all_files = []
            for pattern in fallback_patterns:
                files = glob.glob(pattern)
                # Filter out temporary Excel files (starting with ~$) and other temporary files
                filtered_files = [
                    f for f in files
                    if not os.path.basename(f).startswith('~$')
                    and not os.path.basename(f).startswith('.')
                    and os.path.isfile(f)
                ]
                all_files.extend(filtered_files)

            if not all_files:
                return None

            # Remove duplicates while preserving order
            unique_files = list(dict.fromkeys(all_files))

            # Return the most recent file
            latest_file = max(unique_files, key=os.path.getctime)
            console.print(f"[cyan]Found latest analysis file: {os.path.basename(latest_file)}[/cyan]")
            return latest_file

        except Exception as e:
            logger.error(f"Failed to get latest analysis file: {e}")
            return None

    async def _load_analysis_data(self, analysis_file: str) -> bool:
        """Load analysis data from file"""
        try:
            # Load analysis data - check if file is actually JSON regardless of extension
            # Check if file content is JSON by reading first few characters
            with open(analysis_file, 'r', encoding='utf-8') as f:
                first_char = f.read(1)
                f.seek(0)  # Reset file pointer

                if first_char == '{':
                    # File is JSON format
                    json_data = json.load(f)

                    # Store the original JSON data for package extraction
                    self.original_json_data = json_data
                    self.analysis_file_path = analysis_file

                    # Extract elements from JSON structure
                    if 'analysis_data' in json_data:
                        # Handle live analysis JSON structure
                        self.analysis_data = pd.DataFrame(json_data['analysis_data'])
                        console.print(f"[cyan]Loaded analysis data from JSON: {len(self.analysis_data)} elements[/cyan]")
                    elif 'elements' in json_data:
                        # Handle standard JSON structure
                        self.analysis_data = pd.DataFrame(json_data['elements'])
                        console.print(f"[cyan]Loaded analysis data from JSON: {len(self.analysis_data)} elements[/cyan]")
                    else:
                        console.print("[red]Invalid JSON structure: no 'elements' or 'analysis_data' key found[/red]")
                        return False

                    # Initialize package information once for all tickets
                    self._initialize_package_info()
                    return True
                else:
                    # File is Excel format
                    raise ValueError("Not a JSON file")

        except (json.JSONDecodeError, ValueError, UnicodeDecodeError):
            # File is not JSON or has encoding issues, try Excel
            # Read Excel file - try to read from Elements sheet first
            try:
                # Try to read from Elements sheet (detailed element data) with explicit engine
                self.analysis_data = pd.read_excel(analysis_file, sheet_name='Elements', engine='openpyxl')
                console.print(f"[cyan]Loaded detailed element data from Excel 'Elements' sheet: {len(self.analysis_data)} elements[/cyan]")
                return True
            except Exception as e:
                console.print(f"[yellow]Could not read 'Elements' sheet: {e}[/yellow]")
                console.print("[yellow]Trying default sheet...[/yellow]")

                try:
                    # Fallback to default sheet with explicit engine
                    self.analysis_data = pd.read_excel(analysis_file, engine='openpyxl')
                    console.print(f"[cyan]Loaded analysis data from Excel default sheet: {len(self.analysis_data)} elements[/cyan]")
                    return True
                except Exception as e2:
                    console.print(f"[red]Failed to read Excel file with openpyxl engine: {e2}[/red]")
                    console.print("[yellow]Trying with xlrd engine...[/yellow]")
                    try:
                        # Try with xlrd engine as fallback
                        self.analysis_data = pd.read_excel(analysis_file, engine='xlrd')
                        console.print(f"[cyan]Loaded analysis data from Excel with xlrd engine: {len(self.analysis_data)} elements[/cyan]")
                        return True
                    except Exception as e3:
                        console.print(f"[red]Failed to read Excel file with any engine: {e3}[/red]")
                        return False

        except Exception as e:
            logger.error(f"Failed to load analysis data: {e}")
            return False

    def _group_by_features(self) -> Dict[str, pd.DataFrame]:
        """Group analysis data by the 18 main page features"""
        features = {}

        try:
            # Check if analysis_data is available
            if self.analysis_data is None or self.analysis_data.empty:
                console.print("[red]No analysis data available for grouping[/red]")
                return {}

            # Define the 18 main page features + 2 comprehensive testing features as specified by user
            main_page_features = [
                "Rumah Pendidikan",           # 1.1 (image)
                "Jelajahi Beragam Layanan",   # 1.2 (ui text) - shortened for filename
                "Search",                     # 1.3 (search column and button)
                "Ruang GTK",                  # 1.4 (ui text)
                "Ruang Murid",                # 1.5 (ui text)
                "Ruang Sekolah",              # 1.6 (ui text)
                "Ruang Bahasa",               # 1.7 (ui text)
                "Ruang Pemerintah",           # 1.8 (ui text)
                "Ruang Mitra",                # 1.9 (ui text)
                "Ruang Publik",               # 1.10 (ui text)
                "Ruang Orang Tua",            # 1.11 (ui text)
                "Layanan Paling Banyak Diakses", # 1.12 (ui text)
                "Lihat Semua",                # 1.13 (ui text)
                "Butuh Bantuan",              # 1.14 (ui text)
                "Beranda",                    # 1.15 (navigation bar)
                "Cari",                       # 1.16 (navigation bar)
                "Pemberitahuan",              # 1.17 (navigation bar)
                "Akun",                       # 1.18 (navigation bar)
                "End to End Testing",         # 1.19 (comprehensive user journeys)
                "Integration Testing"         # 1.20 (comprehensive integration scenarios)
            ]

            console.print(f"[cyan]🎯 Creating 20 feature files: 18 main page features + 1 end-to-end + 1 integration testing[/cyan]")

            # Create features based on element matching
            for feature_name in main_page_features:
                # Find elements that match this feature
                feature_elements = self._find_elements_for_feature(feature_name)

                if not feature_elements.empty:
                    features[feature_name] = feature_elements
                    console.print(f"[green]✅ {feature_name}: {len(feature_elements)} elements[/green]")
                else:
                    # Create feature with minimal data for comprehensive scenario generation
                    features[feature_name] = self._create_minimal_feature_data(feature_name)
                    console.print(f"[cyan]📝 {feature_name}: Using comprehensive minimal data[/cyan]")

            console.print(f"[bold green]📊 Created {len(features)} feature files: 18 main page features + 1 end-to-end + 1 integration testing[/bold green]")
            return features

        except Exception as e:
            logger.error(f"Failed to group by features: {e}")
            # Return empty dict if there's an error
            return {}

    def _find_elements_for_feature(self, feature_name: str) -> pd.DataFrame:
        """Find elements in analysis data that match the feature"""
        try:
            # Check if analysis_data is available
            if self.analysis_data is None or self.analysis_data.empty:
                return pd.DataFrame()

            # Create search patterns for the feature
            search_patterns = self._create_search_patterns(feature_name)

            # Search in text, content_desc, and unique_description columns
            matching_elements = pd.DataFrame()

            # Handle string columns only (avoid list columns that cause "unhashable type" error)
            searchable_columns = ['text', 'content_desc', 'unique_description', 'adaptive_name', 'Element_Name']

            for column in searchable_columns:
                if column in self.analysis_data.columns:
                    # Check if column contains string data (not lists)
                    try:
                        # First check if any values in the column are lists
                        sample_values = self.analysis_data[column].dropna().head(5)
                        has_lists = any(isinstance(val, list) for val in sample_values)

                        if has_lists:
                            # Skip columns that contain lists
                            logger.debug(f"Skipping column {column} - contains list data")
                            continue

                        # Convert to string and handle NaN values
                        string_series = self.analysis_data[column].astype(str).fillna('')

                        for pattern in search_patterns:
                            # Use string contains on the converted series
                            mask = string_series.str.contains(
                                pattern, case=False, na=False, regex=False
                            )
                            matches = self.analysis_data[mask]
                            if not matches.empty:
                                matching_elements = pd.concat([matching_elements, matches], ignore_index=True)
                    except Exception as col_error:
                        logger.debug(f"Skipping column {column} due to data type issue: {col_error}")
                        continue

            # Remove duplicates
            if not matching_elements.empty:
                matching_elements = matching_elements.drop_duplicates()

            return matching_elements

        except Exception as e:
            # Don't log as error since we have fallback minimal data - just debug
            logger.debug(f"Could not find specific elements for {feature_name}: {e}")
            return pd.DataFrame()

    def _create_search_patterns(self, feature_name: str) -> List[str]:
        """Create search patterns for finding elements related to a feature and its sub-menus"""
        patterns = [feature_name]

        # Add variations and keywords
        feature_lower = feature_name.lower()

        if "ruang" in feature_lower:
            # For "Ruang X" features, also search for just "X"
            base_name = feature_name.replace("Ruang ", "")
            patterns.extend([base_name, feature_lower, base_name.lower()])

            # Add sub-menu patterns for each Ruang feature
            sub_menus = self._get_feature_sub_menus(feature_name)
            patterns.extend(sub_menus)

        elif feature_name == "Search":
            patterns.extend(["search", "cari", "pencarian"])
            # Search sub-menus: filters, categories, recent searches
            patterns.extend(["filter", "kategori", "recent", "history", "advanced"])

        elif feature_name == "Beranda":
            patterns.extend(["beranda", "home", "tab 1"])
            # Beranda sub-menus: dashboard, quick access, announcements
            patterns.extend(["dashboard", "quick", "pengumuman", "shortcut", "widget"])

        elif feature_name == "Cari":
            patterns.extend(["cari", "search", "tab 2"])
            # Cari sub-menus: similar to Search but in navigation context
            patterns.extend(["filter", "sort", "kategori", "hasil"])

        elif feature_name == "Pemberitahuan":
            patterns.extend(["pemberitahuan", "notification", "tab 3"])
            # Notification sub-menus: categories, settings, history
            patterns.extend(["settings", "kategori", "history", "mark", "clear"])

        elif feature_name == "Akun":
            patterns.extend(["akun", "account", "profile", "tab 4"])
            # Account sub-menus: profile, settings, security, logout
            patterns.extend(["profile", "settings", "security", "logout", "edit", "password"])

        elif "layanan" in feature_lower:
            patterns.extend(["layanan", "service", "paling banyak"])
            # Services sub-menus: categories, popular, recent
            patterns.extend(["kategori", "popular", "terbaru", "semua"])

        elif feature_name == "Lihat Semua":
            patterns.extend(["lihat semua", "see all", "view all"])
            # View all sub-menus: sorting, filtering
            patterns.extend(["sort", "filter", "grid", "list"])

        elif feature_name == "Butuh Bantuan":
            patterns.extend(["butuh bantuan", "help", "bantuan"])
            # Help sub-menus: FAQ, contact, tutorials
            patterns.extend(["faq", "contact", "tutorial", "guide", "support"])

        elif feature_name == "Rumah Pendidikan":
            patterns.extend(["rumah pendidikan", "logo", "title"])
            # Main app sub-menus: about, version, info
            patterns.extend(["about", "version", "info", "tentang"])

        elif "jelajahi" in feature_lower:
            patterns.extend(["jelajahi", "explore", "layanan pendidikan"])
            # Explore sub-menus: categories, featured, new
            patterns.extend(["kategori", "featured", "terbaru", "populer"])

        return patterns

    def _get_feature_sub_menus(self, feature_name: str) -> List[str]:
        """Get comprehensive sub-menu patterns for each feature based on educational app structure"""
        sub_menus = []

        if feature_name == "Ruang GTK":
            sub_menus.extend([
                # Professional development sub-menus
                "pelatihan", "training", "sertifikasi", "certification",
                "webinar", "workshop", "modul", "module",
                "kompetensi", "competency", "karir", "career",
                "komunitas", "community", "forum", "diskusi"
            ])

        elif feature_name == "Ruang Murid":
            sub_menus.extend([
                # Student learning sub-menus
                "pembelajaran", "learning", "tugas", "assignment",
                "kuis", "quiz", "ujian", "exam",
                "materi", "material", "video", "ebook",
                "progress", "nilai", "grade", "rapor",
                "ekstrakurikuler", "club", "kompetisi"
            ])

        elif feature_name == "Ruang Sekolah":
            sub_menus.extend([
                # School management sub-menus
                "administrasi", "administration", "kurikulum", "curriculum",
                "jadwal", "schedule", "kalender", "calendar",
                "guru", "teacher", "siswa", "student",
                "kelas", "class", "mata pelajaran", "subject",
                "laporan", "report", "statistik", "analytics"
            ])

        elif feature_name == "Ruang Bahasa":
            sub_menus.extend([
                # Language learning sub-menus
                "kosakata", "vocabulary", "grammar", "tata bahasa",
                "pronunciation", "pelafalan", "listening", "speaking",
                "reading", "writing", "conversation", "percakapan",
                "level", "beginner", "intermediate", "advanced"
            ])

        elif feature_name == "Ruang Pemerintah":
            sub_menus.extend([
                # Government services sub-menus
                "kebijakan", "policy", "regulasi", "regulation",
                "program", "inisiatif", "initiative", "bantuan",
                "beasiswa", "scholarship", "dana", "funding",
                "pengumuman", "announcement", "informasi"
            ])

        elif feature_name == "Ruang Mitra":
            sub_menus.extend([
                # Partnership sub-menus
                "kerjasama", "partnership", "kolaborasi", "collaboration",
                "sponsor", "donasi", "donation", "program bersama",
                "industri", "industry", "universitas", "university",
                "organisasi", "organization", "komunitas"
            ])

        elif feature_name == "Ruang Publik":
            sub_menus.extend([
                # Public space sub-menus
                "diskusi", "discussion", "forum", "sharing",
                "pengalaman", "experience", "tips", "advice",
                "artikel", "article", "blog", "berita", "news",
                "event", "acara", "kegiatan", "activity"
            ])

        elif feature_name == "Ruang Orang Tua":
            sub_menus.extend([
                # Parent space sub-menus
                "monitoring", "pemantauan", "progress anak", "child progress",
                "komunikasi", "communication", "konsultasi", "consultation",
                "tips parenting", "parenting", "panduan", "guide",
                "jadwal anak", "child schedule", "nilai", "grades"
            ])

        return sub_menus

    async def _generate_sub_menu_scenarios(
        self, feature_name: str, feature_data: pd.DataFrame,
        clickable_elements: pd.DataFrame, input_elements: pd.DataFrame
    ) -> List[str]:
        """Generate comprehensive sub-menu specific scenarios for each feature"""
        scenarios = []

        try:
            console.print(f"[cyan]🎯 Generating sub-menu scenarios for {feature_name}...[/cyan]")

            # Get sub-menus for this feature
            sub_menus = self._get_feature_sub_menus(feature_name)

            if not sub_menus:
                console.print(f"[dim]⚪ No specific sub-menus defined for {feature_name}[/dim]")
                return scenarios

            # Generate scenarios for key sub-menus (limit to avoid too many scenarios)
            key_sub_menus = sub_menus[:4]  # Take first 4 most important sub-menus

            for sub_menu in key_sub_menus:
                # 1. Sub-menu navigation scenario
                nav_scenario = self._generate_sub_menu_navigation_scenario(feature_name, sub_menu)
                if nav_scenario:
                    scenarios.append(nav_scenario)

                # 2. Sub-menu functionality scenario
                func_scenario = self._generate_sub_menu_functionality_scenario(feature_name, sub_menu)
                if func_scenario:
                    scenarios.append(func_scenario)

            # 3. Sub-menu integration scenario (how sub-menus work together)
            if len(key_sub_menus) >= 2:
                integration_scenario = self._generate_sub_menu_integration_scenario(
                    feature_name, key_sub_menus[:2]
                )
                if integration_scenario:
                    scenarios.append(integration_scenario)

            console.print(f"[green]✅ Generated {len(scenarios)} sub-menu scenarios for {feature_name}[/green]")
            return scenarios

        except Exception as e:
            logger.error(f"Failed to generate sub-menu scenarios for {feature_name}: {e}")
            return []

    def _generate_sub_menu_navigation_scenario(self, feature_name: str, sub_menu: str) -> str:
        """Generate navigation scenario for a specific sub-menu with comprehensive assertions"""
        scenario_title = f"Navigate to {sub_menu} in {feature_name}"

        # Get specific assertions for this sub-menu type
        assertions = self._get_sub_menu_assertions(sub_menu)

        scenario = f"""
  Scenario: {scenario_title}
    Given I am on the {feature_name} screen
    When I launch the mobile application
    Then I should see the {feature_name} interface
    And the screen should be responsive
    When I look for "{sub_menu}" option
    Then I should see the "{sub_menu}" menu item
    And the "{sub_menu}" menu item should be clickable
    When I tap on "{sub_menu}" menu item
    Then I should navigate to the {sub_menu} section
    And the {sub_menu} content should be displayed
    And the page title should contain "{sub_menu}"
{assertions}
    And I should be able to interact with {sub_menu} features
    And the navigation should be successful
"""
        return scenario

    def _generate_sub_menu_functionality_scenario(self, feature_name: str, sub_menu: str) -> str:
        """Generate functionality scenario for a specific sub-menu with comprehensive assertions"""
        scenario_title = f"Use {sub_menu} functionality in {feature_name}"

        # Customize functionality based on sub-menu type
        functionality_steps = self._get_sub_menu_functionality_steps(feature_name, sub_menu)
        validation_steps = self._get_sub_menu_validation_steps(sub_menu)

        scenario = f"""
  Scenario: {scenario_title}
    Given I am in the {sub_menu} section of {feature_name}
    When I access the {sub_menu} features
    Then I should see available {sub_menu} options
    And the {sub_menu} interface should be fully loaded
{functionality_steps}
{validation_steps}
    And all {sub_menu} features should work correctly
    And the application should remain responsive
    And I should be able to return to the main {feature_name} screen
    And the return navigation should work properly
"""
        return scenario

    def _get_sub_menu_assertions(self, sub_menu: str) -> str:
        """Get specific assertion steps for different sub-menu types"""
        assertions = ""

        sub_menu_lower = sub_menu.lower()

        if "pelatihan" in sub_menu_lower or "training" in sub_menu_lower:
            assertions = """    And I should see "Course" or "Training" content
    And I should see course listings or training materials
    And the training content should be accessible"""

        elif "tugas" in sub_menu_lower or "assignment" in sub_menu_lower:
            assertions = """    And I should see "Assignment" or "Task" content
    And I should see assignment list or task details
    And assignment status should be visible"""

        elif "progress" in sub_menu_lower or "nilai" in sub_menu_lower:
            assertions = """    And I should see "Progress" or "Grade" content
    And I should see progress indicators or grade information
    And progress data should be displayed correctly"""

        elif "forum" in sub_menu_lower or "diskusi" in sub_menu_lower:
            assertions = """    And I should see "Forum" or "Discussion" content
    And I should see discussion threads or forum posts
    And forum functionality should be available"""

        elif "settings" in sub_menu_lower or "pengaturan" in sub_menu_lower:
            assertions = """    And I should see "Settings" or "Configuration" content
    And I should see configurable options or preferences
    And settings should be modifiable"""

        elif "profile" in sub_menu_lower or "profil" in sub_menu_lower:
            assertions = """    And I should see "Profile" or "Account" content
    And I should see personal information or account details
    And profile information should be editable"""

        elif "search" in sub_menu_lower or "filter" in sub_menu_lower:
            assertions = """    And I should see "Search" or "Filter" content
    And I should see search functionality or filter options
    And search/filter controls should be functional"""

        else:
            # Generic assertions for any sub-menu
            assertions = f"""    And I should see "{sub_menu}" related content
    And the {sub_menu} functionality should be available
    And all {sub_menu} elements should be properly displayed"""

        return assertions

    def _generate_sub_menu_integration_scenario(self, feature_name: str, sub_menus: List[str]) -> str:
        """Generate scenario testing integration between sub-menus"""
        sub_menu1, sub_menu2 = sub_menus[0], sub_menus[1]
        scenario_title = f"Navigate between {sub_menu1} and {sub_menu2} in {feature_name}"

        scenario = f"""
  Scenario: {scenario_title}
    Given I am on the {feature_name} screen
    When I navigate to "{sub_menu1}" section
    Then I should see {sub_menu1} content
    When I switch to "{sub_menu2}" section
    Then I should see {sub_menu2} content
    And the transition should be smooth
    When I go back to "{sub_menu1}" section
    Then my previous {sub_menu1} state should be preserved
    And navigation between sub-menus should work seamlessly
"""
        return scenario

    def _get_sub_menu_functionality_steps(self, feature_name: str, sub_menu: str) -> str:
        """Get specific functionality steps based on feature and sub-menu type"""
        steps = ""

        # Educational app specific functionality
        if "pelatihan" in sub_menu.lower() or "training" in sub_menu.lower():
            steps = """    When I browse available training courses
    Then I should see course listings with details
    When I select a training course
    Then I should see course information and enrollment options"""

        elif "tugas" in sub_menu.lower() or "assignment" in sub_menu.lower():
            steps = """    When I view my assignments
    Then I should see assignment list with due dates
    When I open an assignment
    Then I should see assignment details and submission options"""

        elif "progress" in sub_menu.lower() or "nilai" in sub_menu.lower():
            steps = """    When I check my progress
    Then I should see progress indicators and statistics
    When I view detailed progress
    Then I should see comprehensive progress breakdown"""

        elif "forum" in sub_menu.lower() or "diskusi" in sub_menu.lower():
            steps = """    When I browse forum topics
    Then I should see discussion threads
    When I participate in discussions
    Then I should be able to post and reply to messages"""

        elif "settings" in sub_menu.lower() or "pengaturan" in sub_menu.lower():
            steps = """    When I access settings options
    Then I should see configurable preferences
    When I modify settings
    Then changes should be saved and applied"""

        elif "profile" in sub_menu.lower() or "profil" in sub_menu.lower():
            steps = """    When I view my profile information
    Then I should see personal details and preferences
    When I edit profile information
    Then I should be able to update and save changes"""

        else:
            # Generic functionality steps
            steps = f"""    When I interact with {sub_menu} features
    Then I should see relevant {sub_menu} content
    When I perform {sub_menu} actions
    Then the actions should execute successfully"""

        return steps

    def _get_sub_menu_validation_steps(self, sub_menu: str) -> str:
        """Get specific validation/assertion steps for different sub-menu types"""
        validation_steps = ""

        sub_menu_lower = sub_menu.lower()

        if "pelatihan" in sub_menu_lower or "training" in sub_menu_lower:
            validation_steps = """    Then the training course should be accessible
    And course details should be displayed correctly
    And enrollment functionality should work
    And course progress should be trackable"""

        elif "tugas" in sub_menu_lower or "assignment" in sub_menu_lower:
            validation_steps = """    Then assignment details should be complete
    And due dates should be clearly visible
    And submission functionality should be available
    And assignment status should be accurate"""

        elif "progress" in sub_menu_lower or "nilai" in sub_menu_lower:
            validation_steps = """    Then progress data should be accurate
    And progress indicators should be meaningful
    And grade information should be up-to-date
    And progress history should be accessible"""

        elif "forum" in sub_menu_lower or "diskusi" in sub_menu_lower:
            validation_steps = """    Then forum posts should be readable
    And discussion threads should be organized
    And posting functionality should work
    And user interactions should be possible"""

        elif "settings" in sub_menu_lower or "pengaturan" in sub_menu_lower:
            validation_steps = """    Then settings should be configurable
    And changes should be saved properly
    And preferences should be applied immediately
    And settings should persist across sessions"""

        elif "profile" in sub_menu_lower or "profil" in sub_menu_lower:
            validation_steps = """    Then profile information should be accurate
    And personal details should be editable
    And profile updates should be saved
    And profile picture should display correctly"""

        elif "search" in sub_menu_lower or "filter" in sub_menu_lower:
            validation_steps = """    Then search results should be relevant
    And filter options should work correctly
    And search functionality should be responsive
    And results should be properly formatted"""

        else:
            # Generic validation steps
            validation_steps = f"""    Then {sub_menu} functionality should be validated
    And {sub_menu} data should be accurate
    And {sub_menu} interactions should work properly
    And {sub_menu} content should be accessible"""

        return validation_steps

    def _generate_package_abbreviation(self, package_name: str) -> str:
        """Generate intelligent abbreviation from package name for ticket numbering"""
        try:
            if not package_name or not isinstance(package_name, str):
                return "APP"  # Default fallback

            # Remove common prefixes and clean the package name
            cleaned_package = package_name.lower()

            # Remove common package prefixes
            prefixes_to_remove = ["com.", "org.", "net.", "id.", "co.", "app."]
            for prefix in prefixes_to_remove:
                if cleaned_package.startswith(prefix):
                    cleaned_package = cleaned_package[len(prefix):]
                    break

            # Split by dots and underscores to get meaningful parts
            parts = []
            for part in cleaned_package.replace("_", ".").split("."):
                if part and len(part) > 1:  # Skip single characters and empty parts
                    parts.append(part)

            # Generate abbreviation based on package structure
            abbreviation = self._create_smart_abbreviation(parts)

            console.print(f"[cyan]📝 Package: {package_name} → Abbreviation: {abbreviation}[/cyan]")
            return abbreviation

        except Exception as e:
            logger.error(f"Failed to generate abbreviation for package {package_name}: {e}")
            return "APP"  # Safe fallback

    def _create_smart_abbreviation(self, parts: List[str]) -> str:
        """Create intelligent abbreviation from package parts"""
        if not parts:
            return "APP"

        abbreviation = ""

        # Handle different package structures
        if len(parts) == 1:
            # Single part: take first 3-4 characters
            part = parts[0]
            if len(part) <= 4:
                abbreviation = part.upper()
            else:
                abbreviation = part[:4].upper()

        elif len(parts) == 2:
            # Two parts: take 2-3 chars from each
            part1, part2 = parts[0], parts[1]

            # Special handling for known patterns
            if "kemendikdasmen" in part1.lower():
                # kemendikdasmen → KDDM (Kementerian Pendidikan Dasar Menengah)
                abbr1 = "KDDM"
            elif len(part1) <= 3:
                abbr1 = part1.upper()
            else:
                abbr1 = part1[:3].upper()

            if "rumahpendidikan" in part2.lower() or "rumah" in part2.lower():
                # rumahpendidikan → RP (Rumah Pendidikan)
                abbr2 = "RP"
            elif len(part2) <= 3:
                abbr2 = part2.upper()
            else:
                abbr2 = part2[:3].upper()

            abbreviation = abbr1 + abbr2

        elif len(parts) >= 3:
            # Three or more parts: take 1-2 chars from each, max 6 chars total
            for i, part in enumerate(parts[:3]):  # Limit to first 3 parts
                if "kemendikdasmen" in part.lower():
                    abbreviation += "KD"
                elif "rumah" in part.lower():
                    abbreviation += "R"
                elif "pendidikan" in part.lower():
                    abbreviation += "P"
                elif len(part) >= 2:
                    abbreviation += part[:2].upper()
                else:
                    abbreviation += part.upper()

        # Ensure abbreviation is reasonable length (3-8 characters)
        if len(abbreviation) < 3:
            abbreviation = abbreviation.ljust(3, 'X')
        elif len(abbreviation) > 8:
            abbreviation = abbreviation[:8]

        # CRITICAL: Apply safety filter to prevent inappropriate abbreviations
        safe_abbreviation = self._apply_safety_filter(abbreviation, parts)

        return safe_abbreviation

    def _apply_safety_filter(self, abbreviation: str, original_parts: List[str]) -> str:
        """Apply comprehensive safety filter to prevent inappropriate abbreviations"""
        try:
            # Convert to uppercase for consistent checking
            abbrev_upper = abbreviation.upper()

            # Comprehensive list of inappropriate terms to avoid
            inappropriate_terms = {
                # Explicit inappropriate terms
                'SEX', 'PORN', 'XXX', 'FUCK', 'SHIT', 'DAMN', 'HELL',
                'ASS', 'BITCH', 'SLUT', 'WHORE', 'DICK', 'COCK', 'PUSSY',
                'TITS', 'BOOBS', 'NUDE', 'NAKED', 'RAPE', 'KILL', 'DIE',

                # Potentially offensive combinations
                'FAG', 'GAY', 'HOMO', 'NAZI', 'KKK', 'ISIS', 'HATE',
                'DRUG', 'WEED', 'METH', 'COKE', 'HEROIN', 'CRACK',

                # Rude/inappropriate slang
                'SUCK', 'BLOW', 'LICK', 'BANG', 'SCREW', 'HUMP',
                'PISS', 'CRAP', 'FART', 'BUTT', 'BOOTY', 'SEXY',

                # Potentially problematic abbreviations
                'WTF', 'STFU', 'GTFO', 'LMAO', 'ROFL', 'OMG',
                'YOLO', 'SWAG', 'THOT', 'SIMP', 'INCEL',

                # Religious/cultural sensitivity
                'JESUS', 'CHRIST', 'ALLAH', 'BUDDHA', 'SATAN', 'DEVIL',

                # Violence-related
                'GUN', 'BOMB', 'TERROR', 'MURDER', 'BLOOD', 'DEAD',
                'KNIFE', 'SWORD', 'WEAPON', 'FIGHT', 'WAR', 'ATTACK',

                # Gambling/inappropriate activities
                'BET', 'GAMBLE', 'CASINO', 'POKER', 'STRIP', 'ESCORT',

                # Common inappropriate combinations that might form
                'ANAL', 'ORAL', 'BDSM', 'FETISH', 'KINKY', 'HORNY',
                'MILF', 'GILF', 'DILF', 'PAWG', 'BBC', 'BWC',

                # Potentially offensive in different languages/contexts
                'PUTA', 'MERDE', 'SCHEISSE', 'KURWA', 'BLYAT', 'CYKA'
            }

            # Check if abbreviation contains any inappropriate terms
            if abbrev_upper in inappropriate_terms:
                console.print(f"[yellow]⚠️ Inappropriate abbreviation detected: {abbrev_upper}[/yellow]")
                return self._generate_safe_alternative(original_parts)

            # Check for partial matches (abbreviation contains inappropriate substring)
            for term in inappropriate_terms:
                if term in abbrev_upper and len(term) >= 3:  # Only check meaningful substrings
                    console.print(f"[yellow]⚠️ Potentially inappropriate substring detected: {term} in {abbrev_upper}[/yellow]")
                    return self._generate_safe_alternative(original_parts)

            # Check for common inappropriate patterns
            inappropriate_patterns = [
                r'.*SEX.*', r'.*PORN.*', r'.*FUCK.*', r'.*SHIT.*',
                r'.*ASS$', r'^ASS.*', r'.*DICK.*', r'.*COCK.*',
                r'.*69.*', r'.*420.*'  # Common inappropriate numbers
            ]

            import re
            for pattern in inappropriate_patterns:
                if re.match(pattern, abbrev_upper):
                    console.print(f"[yellow]⚠️ Inappropriate pattern detected: {pattern} matches {abbrev_upper}[/yellow]")
                    return self._generate_safe_alternative(original_parts)

            # Additional safety check: avoid abbreviations that sound inappropriate when pronounced
            phonetic_inappropriate = {
                'PHUK', 'FAWK', 'SHYT', 'BYTH', 'DYKK', 'KAWK',
                'PHORN', 'SEKS', 'AZSS', 'BTCH', 'SLYT', 'WHYR'
            }

            if abbrev_upper in phonetic_inappropriate:
                console.print(f"[yellow]⚠️ Phonetically inappropriate abbreviation detected: {abbrev_upper}[/yellow]")
                return self._generate_safe_alternative(original_parts)

            # If all checks pass, return the original abbreviation
            return abbreviation

        except Exception as e:
            logger.error(f"Error in safety filter: {e}")
            # If safety filter fails, generate a safe alternative
            return self._generate_safe_alternative(original_parts)

    def _generate_safe_alternative(self, original_parts: List[str]) -> str:
        """Generate a safe alternative abbreviation when inappropriate content is detected"""
        try:
            # Strategy 1: Use different character combinations
            safe_abbreviation = ""

            for part in original_parts[:3]:  # Limit to 3 parts
                if len(part) >= 3:
                    # Take 1st and 3rd character to avoid common inappropriate combinations
                    safe_abbreviation += part[0].upper() + part[2].upper()
                elif len(part) >= 2:
                    # Take 1st and last character
                    safe_abbreviation += part[0].upper() + part[-1].upper()
                else:
                    safe_abbreviation += part.upper()

            # Ensure minimum length
            if len(safe_abbreviation) < 3:
                safe_abbreviation += "APP"

            # Truncate if too long
            if len(safe_abbreviation) > 8:
                safe_abbreviation = safe_abbreviation[:8]

            # Double-check the safe alternative isn't also inappropriate
            if self._is_abbreviation_safe(safe_abbreviation):
                console.print(f"[green]✅ Generated safe alternative: {safe_abbreviation}[/green]")
                return safe_abbreviation
            else:
                # Strategy 2: Use generic safe abbreviation with numbers
                import hashlib
                hash_suffix = hashlib.md5(''.join(original_parts).encode()).hexdigest()[:3].upper()
                safe_fallback = f"APP{hash_suffix}"
                console.print(f"[green]✅ Using safe fallback: {safe_fallback}[/green]")
                return safe_fallback

        except Exception as e:
            logger.error(f"Error generating safe alternative: {e}")
            return "APPLIC"  # Ultimate safe fallback

    def _is_abbreviation_safe(self, abbreviation: str) -> bool:
        """Quick safety check for an abbreviation"""
        try:
            # Re-run basic safety checks
            abbrev_upper = abbreviation.upper()

            basic_inappropriate = {
                'SEX', 'PORN', 'FUCK', 'SHIT', 'ASS', 'DICK', 'COCK',
                'BITCH', 'SLUT', 'WHORE', 'DAMN', 'HELL', 'KILL'
            }

            return abbrev_upper not in basic_inappropriate

        except Exception:
            return False  # If check fails, assume unsafe

    def _extract_package_from_analysis(self) -> str:
        """Extract the most common valid package name from analysis data"""
        try:
            # Strategy 1: Check if we have the original JSON data with metadata
            if hasattr(self, 'original_json_data') and self.original_json_data:
                # Look for package in session metadata
                if 'session_metadata' in self.original_json_data:
                    metadata = self.original_json_data['session_metadata']
                    if 'app_package' in metadata and metadata['app_package']:
                        package = metadata['app_package'].strip()
                        if package and package != "" and not self._is_invalid_package(package):
                            console.print(f"[green]📦 Found valid package in metadata: {package}[/green]")
                            return package

            # Strategy 2: Find most common valid package from DataFrame
            if self.analysis_data is not None and not self.analysis_data.empty:
                # Look for package in different possible columns
                package_columns = ['package', 'app_package', 'package_name', 'application_package']

                for col in package_columns:
                    if col in self.analysis_data.columns:
                        # Get all package values and filter out invalid ones
                        package_values = self.analysis_data[col].dropna()
                        package_values = package_values[package_values != ""]  # Remove empty strings

                        # Filter out invalid packages (unknown, null, etc.)
                        valid_packages = []
                        for pkg in package_values:
                            if isinstance(pkg, str) and pkg.strip() and not self._is_invalid_package(pkg.strip()):
                                valid_packages.append(pkg.strip())

                        if valid_packages:
                            # Find the most common valid package
                            from collections import Counter
                            package_counts = Counter(valid_packages)
                            most_common_package = package_counts.most_common(1)[0][0]

                            console.print(f"[green]📦 Found most common valid package: {most_common_package} (appears {package_counts[most_common_package]} times)[/green]")
                            return most_common_package

            # Strategy 3: Extract from filename if available
            if hasattr(self, 'analysis_file_path') and self.analysis_file_path:
                filename = os.path.basename(self.analysis_file_path)
                # Extract package from filename like: com_kemendikdasmen_rumahpendidikan_20250614_035245_live_analysis.json
                if filename.startswith('com_') and '_live_analysis.json' in filename:
                    # Remove the timestamp and suffix
                    package_part = filename.replace('_live_analysis.json', '')
                    # Find the last timestamp pattern and remove it
                    import re
                    package_part = re.sub(r'_\d{8}_\d{6}$', '', package_part)
                    # Convert underscores back to dots
                    package = package_part.replace('_', '.')

                    if not self._is_invalid_package(package):
                        console.print(f"[green]📦 Extracted valid package from filename: {package}[/green]")
                        return package

            console.print(f"[yellow]⚠️ No valid package found, using default[/yellow]")
            return "com.unknown.app"

        except Exception as e:
            logger.error(f"Failed to extract package from analysis data: {e}")
            console.print(f"[red]❌ Package extraction failed: {e}[/red]")
            return "com.unknown.app"

    def _is_invalid_package(self, package: str) -> bool:
        """Check if a package name is invalid (unknown, null, etc.)"""
        if not package or not isinstance(package, str):
            return True

        package_lower = package.lower().strip()

        # List of invalid package patterns
        invalid_patterns = [
            'unknown',
            'null',
            'none',
            'com.unknown',
            'com.unknown.app',
            'com.example',
            'com.test',
            'android',
            'system',
            'launcher'
        ]

        # Check if package contains any invalid patterns
        for pattern in invalid_patterns:
            if pattern in package_lower:
                return True

        # Check if package is too short or doesn't follow proper format
        if len(package) < 5 or not package.startswith('com.'):
            return True

        return False

    def _initialize_package_info(self) -> None:
        """Initialize and cache package information for consistent ticket numbering"""
        try:
            # Extract package name once and cache it
            self._cached_package_name = self._extract_package_from_analysis()
            self._cached_package_abbreviation = self._generate_package_abbreviation(self._cached_package_name)

            console.print(f"[green]📦 Initialized package info for all tickets:[/green]")
            console.print(f"[cyan]   • Package: {self._cached_package_name}[/cyan]")
            console.print(f"[cyan]   • Abbreviation: {self._cached_package_abbreviation}[/cyan]")

        except Exception as e:
            logger.error(f"Failed to initialize package info: {e}")
            # Set defaults
            self._cached_package_name = "com.unknown.app"
            self._cached_package_abbreviation = "UNKAPP"
            console.print(f"[yellow]⚠️ Using default package info due to error: {e}[/yellow]")

    def _get_cached_package_info(self) -> tuple[str, str]:
        """Get cached package name and abbreviation"""
        if self._cached_package_name is None or self._cached_package_abbreviation is None:
            # Initialize if not already done
            self._initialize_package_info()

        # Ensure we have valid strings (should never be None after initialization)
        package_name = self._cached_package_name or "com.unknown.app"
        package_abbrev = self._cached_package_abbreviation or "UNKAPP"

        return package_name, package_abbrev

    def _create_minimal_feature_data(self, feature_name: str) -> pd.DataFrame:
        """Create minimal feature data for comprehensive scenario generation"""
        # Create a minimal DataFrame with basic structure for scenario generation
        minimal_data = pd.DataFrame([{
            'screen_name': feature_name,
            'text': feature_name,
            'clickable': True,
            'scrollable': False,
            'class_name': 'android.widget.Button',
            'element_type': 'button',
            'capabilities': 'clickable',
            'content_desc': f'{feature_name} element',
            'resource_id': f'com.app:id/{feature_name.lower().replace(" ", "_")}',
            'unique_description': f'{feature_name} main feature element'
        }])

        return minimal_data

    def _should_include_security_testing(self, feature_name: str) -> bool:
        """Determine if a feature should include security testing scenarios"""

        # Features that should have security testing
        security_applicable_features = [
            "Search",                     # Input validation, injection attacks
            "Akun",                      # Authentication, session management
            "Beranda",                   # Main entry point security
            "Cari",                      # Search functionality security
            "Ruang GTK",                 # User data access
            "Ruang Murid",               # Student data security
            "Ruang Sekolah",             # School data security
            "Pemberitahuan",             # Notification security
            "End to End Testing",        # Comprehensive security flows
            "Integration Testing"        # Integration security testing
        ]

        # Features that don't need security testing (informational/static content)
        non_security_features = [
            "Rumah Pendidikan",          # Logo/branding element
            "Jelajahi Beragam Layanan",  # Static content display
            "Ruang Bahasa",              # Language content (mostly static)
            "Ruang Pemerintah",          # Government info (mostly static)
            "Ruang Mitra",               # Partner info (mostly static)
            "Ruang Publik",              # Public info (mostly static)
            "Ruang Orang Tua",           # Parent info (mostly static)
            "Layanan Paling Banyak Diakses", # Service listing (mostly static)
            "Lihat Semua",               # View all button (navigation only)
            "Butuh Bantuan"              # Help/support (mostly static)
        ]

        return feature_name in security_applicable_features

    async def _generate_end_to_end_scenarios(self) -> List[str]:
        """Generate comprehensive end-to-end testing scenarios"""
        scenarios = []

        try:
            # 1. Complete User Journey - New User Registration to Course Completion
            scenarios.append("""
  Scenario: Complete new user onboarding and course enrollment journey
    Given I am a new user opening the Rumah Pendidikan app for the first time
    When I navigate through the welcome screens
    And I create a new account with valid credentials
    And I complete my profile setup
    And I explore the main dashboard features
    And I search for available courses using the search functionality
    And I select and enroll in a course from "Ruang GTK"
    And I navigate to "Ruang Murid" to access learning materials
    And I complete a learning module
    And I check my progress in the account section
    Then I should see my enrollment status updated
    And my learning progress should be tracked correctly
    And all navigation between sections should work seamlessly
""")

            # 2. Multi-Feature Integration Test
            scenarios.append("""
  Scenario: Cross-feature integration and data consistency test
    Given I am logged into the Rumah Pendidikan app
    When I perform actions across multiple features
    And I add items to favorites from "Layanan Paling Banyak Diakses"
    And I search for content using the main search feature
    And I access different room sections (GTK, Murid, Sekolah, Bahasa)
    And I check notifications for updates
    And I modify my account settings
    And I navigate back to the home screen
    Then all my preferences should be maintained
    And data should be consistent across all features
    And no information should be lost during navigation
""")

            # 3. Offline-Online Synchronization
            scenarios.append("""
  Scenario: Offline usage and data synchronization testing
    Given I am using the Rumah Pendidikan app with internet connection
    When I download content for offline access
    And I disable internet connectivity
    And I continue using available offline features
    And I make changes to my profile and preferences
    And I attempt to access online-only features
    And I re-enable internet connectivity
    Then the app should synchronize all offline changes
    And previously unavailable features should become accessible
    And no data should be lost during the offline period
""")

            # 4. Performance and Load Testing
            scenarios.append("""
  Scenario: App performance under heavy usage and load conditions
    Given I am using the Rumah Pendidikan app
    When I rapidly navigate between all main features
    And I open multiple sections simultaneously
    And I perform intensive search operations
    And I download large educational content
    And I use the app continuously for extended periods
    Then the app should maintain responsive performance
    And memory usage should remain within acceptable limits
    And no crashes or freezes should occur
    And all features should remain fully functional
""")

            # 5. Security and Data Protection End-to-End
            scenarios.append("""
  Scenario: Comprehensive security and data protection validation
    Given I am using the Rumah Pendidikan app with sensitive data
    When I log in with my credentials
    And I access personal information in account settings
    And I perform searches with sensitive terms
    And I interact with educational content
    And I use communication features if available
    And I log out and attempt to access protected areas
    Then all personal data should be properly encrypted
    And unauthorized access should be prevented
    And session management should be secure
    And no sensitive information should be exposed
""")

            # 6. Accessibility and Usability Testing
            scenarios.append("""
  Scenario: Comprehensive accessibility and usability validation
    Given I am using the Rumah Pendidikan app with accessibility needs
    When I navigate using screen reader compatibility
    And I test with different font sizes and contrast settings
    And I use voice commands where supported
    And I navigate using keyboard-only input
    And I test with different device orientations
    Then all features should be accessible to users with disabilities
    And navigation should be intuitive and consistent
    And content should be readable in all accessibility modes
    And user experience should be smooth across all interaction methods
""")

            # 7. Data Backup and Recovery
            scenarios.append("""
  Scenario: Data backup, recovery, and account migration testing
    Given I have been using the Rumah Pendidikan app with accumulated data
    When I backup my account data and preferences
    And I simulate device failure or app reinstallation
    And I restore my account on a new device
    And I verify all my previous data and settings
    Then all my courses, progress, and preferences should be restored
    And no learning history should be lost
    And account settings should be identical to the backup
    And the restoration process should be seamless
""")

            # 8. Multi-Platform Consistency
            scenarios.append("""
  Scenario: Cross-platform consistency and feature parity testing
    Given I am using the Rumah Pendidikan app across different devices
    When I access my account on a mobile device
    And I make changes to my profile and preferences
    And I access the same account on a tablet device
    And I continue my learning activities
    And I switch back to the mobile device
    Then all changes should be synchronized across devices
    And the user experience should be consistent
    And feature functionality should be identical
    And no platform-specific issues should occur
""")

            # COMPREHENSIVE NEGATIVE END-TO-END SCENARIOS (100% Coverage)

            # Network and Connectivity Failures
            scenarios.append("""
  Scenario: End-to-end journey with network connectivity failures
    Given I am using the Rumah Pendidikan app with unstable network
    When I start the user registration process
    And network connection is lost during registration
    Then I should see "Connection lost" error message
    When I retry registration after network restoration
    And I continue with course enrollment
    And network becomes slow during content download
    Then the app should show loading indicators and handle slow connection
    And I should be able to complete the journey when network stabilizes
""")

            # Authentication and Session Failures
            scenarios.append("""
  Scenario: End-to-end journey with authentication failures
    Given I am using the Rumah Pendidikan app
    When I attempt to login with invalid credentials
    Then I should see "Invalid credentials" error message
    When I login with valid credentials
    And my session expires during course navigation
    Then I should be prompted to re-authenticate
    When I re-authenticate successfully
    Then I should be able to continue from where I left off
    And my progress should be preserved
""")

            # Data Corruption and Recovery
            scenarios.append("""
  Scenario: End-to-end journey with data corruption and recovery
    Given I am using the Rumah Pendidikan app with corrupted local data
    When I attempt to access my profile information
    Then I should see "Data corruption detected" message
    When I trigger data recovery process
    And I re-sync my account data
    Then my profile should be restored from server backup
    And I should be able to continue using all features normally
    And data integrity should be maintained throughout the journey
""")

            # Device Resource Limitations
            scenarios.append("""
  Scenario: End-to-end journey under device resource limitations
    Given I am using the Rumah Pendidikan app on a device with low memory
    When I navigate through multiple features simultaneously
    And device memory becomes critically low
    Then the app should optimize memory usage automatically
    When I continue using the app with low storage space
    Then the app should manage storage efficiently
    And core functionality should remain available
    And the user experience should degrade gracefully
""")

            # Concurrent User Actions and Conflicts
            scenarios.append("""
  Scenario: End-to-end journey with concurrent action conflicts
    Given I am using the Rumah Pendidikan app
    When I perform multiple actions simultaneously
    And I tap multiple buttons at the same time
    Then the app should handle concurrent actions gracefully
    When I navigate rapidly between different sections
    And I trigger conflicting operations
    Then the app should prioritize actions appropriately
    And no data conflicts should occur
    And the interface should remain responsive
""")

            # Permission Denials and Security Issues
            scenarios.append("""
  Scenario: End-to-end journey with permission denials
    Given I am using the Rumah Pendidikan app without required permissions
    When I attempt to use camera features without camera permission
    Then I should see "Camera permission required" message
    When I attempt to save files without storage permission
    Then I should see "Storage permission required" message
    When I grant the required permissions
    Then all features should become fully functional
    And the app should work normally throughout the journey
""")

            # Performance Degradation and Recovery
            scenarios.append("""
  Scenario: End-to-end journey with performance degradation
    Given I am using the Rumah Pendidikan app under heavy load
    When I perform intensive operations continuously
    And app performance starts to degrade
    Then the app should show performance optimization options
    When I continue using the app with degraded performance
    Then critical features should remain functional
    And the app should recover performance when load decreases
    And user experience should improve gradually
""")

            # State Transition Errors
            scenarios.append("""
  Scenario: End-to-end journey with invalid state transitions
    Given I am using the Rumah Pendidikan app
    When I attempt to access features in wrong application states
    And I try to navigate to restricted areas
    Then the app should prevent invalid state transitions
    When I trigger unexpected state changes
    Then the app should handle state errors gracefully
    And return to a valid application state
    And provide clear guidance to users
""")

            # COMPREHENSIVE POSITIVE END-TO-END SCENARIOS (100% Coverage)

            # Advanced User Journey Testing
            scenarios.append("""
  Scenario: Advanced user journey with multiple feature integration
    Given I am an experienced user of the Rumah Pendidikan app
    When I perform complex workflows involving multiple features
    And I use advanced features and customization options
    And I integrate different types of content and activities
    Then all advanced features should work seamlessly together
    And complex workflows should execute without issues
    And the user experience should remain smooth throughout
    And all integrations should maintain data consistency
""")

            # Content Creation and Management
            scenarios.append("""
  Scenario: Content creation and management workflow
    Given I am using the Rumah Pendidikan app for content management
    When I create, edit, and organize educational content
    And I use various content types and formats
    And I manage content across different sections
    Then content creation tools should work effectively
    And content should be properly organized and accessible
    And all content management features should function correctly
    And content should be preserved and synchronized properly
""")

            # Collaborative Features and Sharing
            scenarios.append("""
  Scenario: Collaborative features and content sharing
    Given I am using the Rumah Pendidikan app for collaboration
    When I share content and resources with others
    And I participate in collaborative activities
    And I use communication and interaction features
    Then sharing features should work reliably
    And collaborative tools should facilitate effective interaction
    And shared content should be accessible to intended recipients
    And collaboration should enhance the learning experience
""")

            # Personalization and Customization
            scenarios.append("""
  Scenario: Personalization and customization features
    Given I am using the Rumah Pendidikan app with personal preferences
    When I customize my profile and learning preferences
    And I personalize the interface and content display
    And I set up notifications and alerts according to my needs
    Then personalization features should work as expected
    And customizations should be saved and applied consistently
    And the personalized experience should enhance usability
    And preferences should persist across sessions
""")

            # Multi-device Synchronization
            scenarios.append("""
  Scenario: Multi-device synchronization and continuity
    Given I use the Rumah Pendidikan app across multiple devices
    When I start activities on one device and continue on another
    And I make changes and updates on different devices
    And I access my content and progress from various devices
    Then synchronization should work seamlessly across devices
    And my progress and data should be consistent everywhere
    And the experience should be continuous regardless of device
    And no data should be lost during device transitions
""")

            # Advanced Search and Discovery
            scenarios.append("""
  Scenario: Advanced search and content discovery
    Given I am using the Rumah Pendidikan app to find specific content
    When I use advanced search features and filters
    And I explore content recommendations and suggestions
    And I discover new resources through various discovery methods
    Then search functionality should be accurate and comprehensive
    And content discovery should be intuitive and helpful
    And search results should be relevant and well-organized
    And discovery features should enhance content exploration
""")

            console.print(f"[green]🎯 Generated {len(scenarios)} comprehensive end-to-end scenarios with 100% positive and negative coverage[/green]")
            return scenarios

        except Exception as e:
            logger.error(f"Failed to generate end-to-end scenarios: {e}")
            return []

    async def _generate_integration_scenarios(self) -> List[str]:
        """Generate mobile UI-focused integration testing scenarios that make sense for emulator execution"""
        scenarios = []

        try:
            # POSITIVE INTEGRATION SCENARIOS - Mobile UI Focused

            # 1. User Profile Management Integration (Database CRUD through UI)
            scenarios.append("""
  Scenario: Complete user profile management with data synchronization
    Given I launch the "Rumah Pendidikan" application
    When I tap on "Profile" or "Akun" button
    Then I should see the profile screen
    When I tap on "Edit Profile" or "Edit" button
    And I enter "John Doe" in the name field
    And I enter "<EMAIL>" in the email field
    And I select "Teacher" from the role dropdown
    And I tap "Save" or "Simpan" button
    Then I should see "Profile updated successfully" message
    When I navigate back and return to profile
    Then I should see my updated information displayed
    And the profile data should be synchronized across the app
""")

            # 2. Course Enrollment and Progress Integration
            scenarios.append("""
  Scenario: Course enrollment and learning progress integration
    Given I launch the "Rumah Pendidikan" application
    When I tap on "Ruang GTK" or course catalog section
    Then I should see available courses
    When I tap on a course from the list
    And I tap "Enroll" or "Daftar" button
    Then I should see "Successfully enrolled" message
    When I tap "Start Learning" or "Mulai Belajar" button
    And I complete a learning module by tapping through content
    And I tap "Mark as Complete" or "Selesai" button
    Then I should see progress updated in the course
    When I navigate to "My Progress" or "Progres Saya"
    Then I should see my learning progress reflected correctly
""")

            # 3. Authentication and Session Management Integration
            scenarios.append("""
  Scenario: User authentication and session management integration
    Given I launch the "Rumah Pendidikan" application
    When I tap on "Login" or "Masuk" button
    And I enter valid credentials in the login form
    And I tap "Sign In" or "Masuk" button
    Then I should see the main dashboard
    And I should see my user information displayed
    When I navigate to different sections of the app
    Then my session should remain active
    When I tap on "Settings" or "Pengaturan"
    And I tap "Logout" or "Keluar" button
    Then I should be redirected to the login screen
    And my session should be properly terminated
""")

            # 4. Notification and Communication Integration
            scenarios.append("""
  Scenario: Notification and communication system integration
    Given I launch the "Rumah Pendidikan" application
    When I tap on "Notifications" or "Notifikasi" icon
    Then I should see my notification list
    When I tap on a notification from the list
    Then I should be navigated to the relevant content
    When I return to the main screen
    And I tap on "Messages" or "Pesan" if available
    Then I should see my message inbox
    When I tap "Compose" or "Tulis Pesan" button
    And I enter a message and send it
    Then I should see "Message sent successfully"
    And the message should appear in my sent items
""")

            # 5. File and Document Management Integration
            scenarios.append("""
  Scenario: Document upload and download integration
    Given I launch the "Rumah Pendidikan" application
    When I navigate to "My Documents" or "Dokumen Saya"
    Then I should see my document library
    When I tap "Upload" or "Unggah" button
    And I select a document from device storage
    Then I should see "Upload successful" message
    And the document should appear in my library
    When I tap on the uploaded document
    Then I should be able to view or download it
    When I tap "Share" or "Bagikan" button
    Then I should see sharing options
    And I should be able to share the document successfully
""")

            # NEGATIVE INTEGRATION SCENARIOS - Mobile UI Focused

            # 6. Network Connection Error Handling
            scenarios.append("""
  Scenario: Network connection error handling during user actions
    Given I launch the "Rumah Pendidikan" application
    When I tap on "Login" or "Masuk" button
    And I enter valid credentials
    And I tap "Sign In" button while network is disconnected
    Then I should see "Network connection error" message
    When I tap "Retry" button
    And network connection is restored
    Then I should be able to login successfully
    And I should see the main dashboard
    When I navigate to course content while offline
    Then I should see cached content or offline mode message
""")

            # 7. Profile Update Failure Handling
            scenarios.append("""
  Scenario: Profile update failure and data recovery
    Given I launch the "Rumah Pendidikan" application
    And I am logged in to my account
    When I tap on "Profile" or "Akun" button
    And I tap "Edit Profile" button
    And I modify my profile information
    And I tap "Save" button during server maintenance
    Then I should see "Unable to save changes" error message
    And I should see "Retry" and "Save Draft" options
    When I tap "Save Draft" button
    Then my changes should be saved locally
    When I tap "Retry" after server is restored
    Then my profile should be updated successfully
""")

            # 8. Third-Party Integration Failures - Service Unavailability
            scenarios.append("""
  Scenario: Third-party service integration failure handling
    Given the app integrates with external education platforms
    When external learning management systems are down
    And partner institution APIs return authentication errors
    And social platform sharing services are unavailable
    And certificate verification services fail to respond
    Then the app should continue functioning without external services
    And users should be informed about unavailable features
    And the app should queue operations for retry when services recover
    And alternative workflows should be provided where possible
    And integration failures should not affect core app functionality
""")

            # 9. Real-time Integration Failures - Connection Issues
            scenarios.append("""
  Scenario: Real-time integration failure and connection recovery
    Given the app provides real-time collaborative features
    When WebSocket connections are dropped unexpectedly
    And real-time servers become overloaded and unresponsive
    And network latency causes significant delays
    And concurrent user limits are exceeded
    Then the app should detect connection failures immediately
    And automatic reconnection should be attempted
    And users should be notified of connection status changes
    And offline mode should be activated when real-time features fail
    And data should be synchronized when connections are restored
""")

            # 10. Cross-Platform Integration Issues - Compatibility Problems
            scenarios.append("""
  Scenario: Cross-platform integration compatibility issues
    Given the app runs on multiple platforms and devices
    When iOS and Android versions have different API implementations
    And web browser compatibility issues arise with certain features
    And mobile and desktop versions have data synchronization conflicts
    And different app versions attempt to integrate simultaneously
    Then the app should handle platform-specific integration differences
    And version compatibility should be enforced at the API level
    And data format conflicts should be resolved automatically
    And users should be guided to update incompatible app versions
    And fallback mechanisms should ensure basic functionality across platforms
""")

            # 11. Security Integration Failures - Breach Scenarios
            scenarios.append("""
  Scenario: Security integration failure and breach prevention
    Given the app handles sensitive educational and personal data
    When authentication tokens are compromised or expired
    And unauthorized access attempts are made to integrated services
    And data encryption fails during transmission to external systems
    And security certificates become invalid or expired
    Then the app should immediately detect security violations
    And all compromised sessions should be terminated
    And affected users should be notified of security incidents
    And emergency security protocols should be activated
    And system access should be restricted until security is restored
""")

            # 12. Performance Integration Issues - Load and Stress
            scenarios.append("""
  Scenario: Performance integration issues under high load
    Given the app integrates with multiple services simultaneously
    When thousands of users access integrated services concurrently
    And large data transfers occur between integrated systems
    And complex queries are executed across multiple databases
    And real-time features are used by maximum concurrent users
    Then the app should maintain acceptable performance levels
    And integration bottlenecks should be identified and managed
    And load balancing should distribute requests effectively
    And system resources should be monitored and optimized
    And graceful degradation should occur under extreme load conditions
""")

            # COMPREHENSIVE POSITIVE INTEGRATION SCENARIOS (100% Coverage)

            # Advanced Data Integration and Synchronization
            scenarios.append("""
  Scenario: Advanced data integration and real-time synchronization
    Given I launch the "Rumah Pendidikan" application
    When I perform data operations across multiple features
    And I create content in one feature and access it from another
    And I update information that affects multiple areas
    Then data should be synchronized in real-time across features
    And all related information should be updated consistently
    And data integrity should be maintained throughout the system
    And users should see immediate updates across all relevant areas
""")

            # Cross-Feature Workflow Integration
            scenarios.append("""
  Scenario: Cross-feature workflow integration and continuity
    Given I launch the "Rumah Pendidikan" application
    When I start a workflow in one feature
    And I continue the workflow in related features
    And I complete tasks that span multiple application areas
    Then workflow continuity should be maintained across features
    And context should be preserved during feature transitions
    And the integrated workflow should be seamless and intuitive
    And all workflow steps should execute correctly
""")

            # API and Service Integration
            scenarios.append("""
  Scenario: API and external service integration
    Given I launch the "Rumah Pendidikan" application
    When I use features that integrate with external services
    And I access content from integrated educational platforms
    And I synchronize data with external systems
    Then API integrations should work reliably
    And external content should be accessible and properly formatted
    And data exchange should be secure and accurate
    And service integrations should enhance the user experience
""")

            # Authentication and Security Integration
            scenarios.append("""
  Scenario: Authentication and security system integration
    Given I launch the "Rumah Pendidikan" application
    When I log in and access secure features
    And I navigate between features requiring different permission levels
    And I use features that handle sensitive information
    Then authentication should work consistently across all features
    And security measures should be properly integrated
    And access controls should be enforced appropriately
    And user sessions should be managed securely throughout the app
""")

            # Content Management Integration
            scenarios.append("""
  Scenario: Content management system integration
    Given I launch the "Rumah Pendidikan" application
    When I create, edit, and manage educational content
    And I organize content across different categories and features
    And I share and collaborate on content with others
    Then content management should be integrated across all features
    And content should be properly categorized and searchable
    And sharing and collaboration features should work seamlessly
    And content versioning and history should be maintained
""")

            # Notification and Communication Integration
            scenarios.append("""
  Scenario: Notification and communication system integration
    Given I launch the "Rumah Pendidikan" application
    When I receive notifications from various features
    And I use communication tools integrated throughout the app
    And I manage my notification preferences and settings
    Then notifications should be delivered reliably and timely
    And communication features should be accessible from relevant contexts
    And notification settings should be respected across all features
    And communication should enhance collaboration and engagement
""")

            # Validate and improve all scenarios for mobile UI execution
            improved_scenarios = []
            for scenario in scenarios:
                if self._validate_mobile_ui_scenario(scenario):
                    improved_scenarios.append(scenario)
                else:
                    # Improve abstract scenarios to be mobile UI-focused
                    improved_scenario = self._improve_scenario_for_mobile_ui(scenario, "Integration Testing")
                    improved_scenarios.append(improved_scenario)
                    console.print("[yellow]⚡ Improved abstract scenario to be mobile UI-focused[/yellow]")

            console.print(f"[green]🎯 Generated {len(improved_scenarios)} comprehensive integration scenarios with 100% positive and negative coverage[/green]")
            return improved_scenarios

        except Exception as e:
            logger.error(f"Failed to generate integration scenarios: {e}")
            return []

    def _clean_feature_name(self, screen_name: str) -> str:
        """Clean screen name to create valid feature name"""
        # Remove special characters and make it readable
        feature_name = screen_name.replace("_", " ").replace("-", " ")
        feature_name = " ".join(
            word.capitalize() for word in feature_name.split()
        )
        return feature_name

    async def _generate_feature_scenarios(
        self, feature_name: str, feature_data: pd.DataFrame
    ) -> List[str]:
        """Generate comprehensive test scenarios for maximum coverage"""
        scenarios = []

        try:
            # Get RAG-enhanced context for this feature
            enhanced_context = await self._get_rag_enhanced_context(feature_name, feature_data)

            # Get clickable elements for this feature
            clickable_elements = feature_data[
                feature_data['clickable'] == True
            ]

            # Get input elements (check if capabilities column exists)
            if 'capabilities' in feature_data.columns:
                input_elements = feature_data[
                    feature_data['capabilities'].str.contains(
                        'input_text', na=False
                    )
                ]
            else:
                # Fallback: look for elements that might be input fields
                input_elements = feature_data[
                    (feature_data['element_type'].str.contains('input', case=False, na=False)) |
                    (feature_data['class_name'].str.contains('EditText', case=False, na=False))
                ]

            # Get scrollable elements
            scrollable_elements = feature_data[
                feature_data['scrollable'] == True
            ]

            console.print(f"[cyan]🎯 Generating comprehensive scenarios for {feature_name}...[/cyan]")

            # Special handling for End-to-End Testing feature
            if feature_name == "End to End Testing":
                scenarios = await self._generate_end_to_end_scenarios()
                console.print(f"[green]✅ Generated {len(scenarios)} end-to-end scenarios[/green]")
                return scenarios

            # Special handling for Integration Testing feature
            if feature_name == "Integration Testing":
                scenarios = await self._generate_integration_scenarios()
                console.print(f"[green]✅ Generated {len(scenarios)} integration scenarios[/green]")
                return scenarios

            # 1. POSITIVE SCENARIOS (6-8 scenarios) - Including sub-menu scenarios
            positive_scenarios = await self._generate_comprehensive_positive_scenarios(
                feature_name, feature_data, clickable_elements, input_elements,
                scrollable_elements, enhanced_context
            )
            scenarios.extend(positive_scenarios)

            # 1.1. SUB-MENU SPECIFIC SCENARIOS (3-5 scenarios)
            sub_menu_scenarios = await self._generate_sub_menu_scenarios(
                feature_name, feature_data, clickable_elements, input_elements
            )
            scenarios.extend(sub_menu_scenarios)

            # 2. NEGATIVE SCENARIOS - Generate MORE negative scenarios than positive
            negative_scenarios = await self._generate_enhanced_negative_scenarios(
                feature_name, feature_data, clickable_elements, input_elements, len(positive_scenarios)
            )
            scenarios.extend(negative_scenarios)

            # 3. SECURITY SCENARIOS (2-4 scenarios) - Smart selection
            if self._should_include_security_testing(feature_name):
                security_scenarios = await self._generate_security_scenarios(
                    feature_name, feature_data, clickable_elements, input_elements
                )
                scenarios.extend(security_scenarios)
                console.print(f"[yellow]🔒 Added {len(security_scenarios)} security scenarios for {feature_name}[/yellow]")
            else:
                console.print(f"[dim]⚪ Skipped security testing for {feature_name} (not applicable)[/dim]")

            # 4. EDGE CASE SCENARIOS (2-3 scenarios)
            edge_case_scenarios = await self._generate_edge_case_scenarios(
                feature_name, feature_data, clickable_elements, input_elements
            )
            scenarios.extend(edge_case_scenarios)

            console.print(f"[green]✅ Generated {len(scenarios)} comprehensive scenarios for {feature_name}[/green]")
            return scenarios

        except Exception as e:
            logger.error(
                f"Failed to generate scenarios for {feature_name}: {e}"
            )
            logger.error(f"Exception details: {type(e).__name__}: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return []

    async def _generate_rag_enhanced_navigation_scenario(
        self, feature_name: str, clickable_elements: pd.DataFrame, enhanced_context: Dict[str, Any]
    ) -> Optional[str]:
        """Generate RAG-enhanced navigation scenario with better context understanding"""
        try:
            # Extract meaningful patterns from RAG context
            navigation_patterns = self._extract_navigation_patterns_from_rag(enhanced_context)

            # Get meaningful elements with better descriptions
            meaningful_elements = self._get_meaningful_elements_with_rag(clickable_elements, enhanced_context)

            if not meaningful_elements:
                # Fallback to basic generation if no meaningful elements found
                return self._generate_navigation_scenario(feature_name, clickable_elements)

            # Create context-aware scenario title
            scenario_title = self._create_contextual_scenario_title(feature_name, "navigation", navigation_patterns)

            scenario = f"""
  Scenario: {scenario_title}
    Given I am on the {feature_name} screen
    When I launch the mobile application
    Then I should see the {feature_name} interface
"""

            # Generate context-aware steps
            for element, element_desc, context_info in meaningful_elements[:3]:  # Limit to 3 for quality
                # Add verification step with context
                scenario += f"    And I should see {element_desc}\n"

                # Add interaction step with purpose
                purpose = context_info.get('purpose', 'interact with the interface')
                scenario += f"    When I tap on {element_desc} to {purpose}\n"
                scenario += f"    Then the application should respond appropriately\n"

                # Add validation based on element type
                if context_info.get('expected_action'):
                    scenario += f"    And {context_info['expected_action']}\n"

            # Check for duplicates
            if self._is_scenario_duplicate(scenario):
                return None

            return scenario

        except Exception as e:
            logger.error(f"Failed to generate RAG-enhanced navigation scenario: {e}")
            # Fallback to basic generation
            return self._generate_navigation_scenario(feature_name, clickable_elements)

    def _generate_professional_navigation_scenario(
        self, feature_name: str, clickable_elements: pd.DataFrame
    ) -> Optional[str]:
        """Generate professional-style navigation test scenario inspired by team's API style"""
        scenario_title = f"Navigate and validate {feature_name} functionality"

        # Use professional patterns
        auth_step = self.professional_patterns["setup_authentication"][0]
        nav_step = self.professional_patterns["navigate_to_screen"][0].format(screen_name=feature_name)
        verify_step = self.professional_patterns["verify_response"][0]

        scenario = f"""
  Scenario: {scenario_title}
    {auth_step}
    {nav_step}
    {verify_step}
"""

        # Add meaningful element interactions with professional patterns
        meaningful_elements = []
        for _, element in clickable_elements.iterrows():
            element_desc = self._get_element_description(element)
            if (element_desc and
                element_desc != "the UI element" and
                "unknown" not in element_desc.lower()):
                meaningful_elements.append((element, element_desc))
                if len(meaningful_elements) >= 3:  # Limit for quality
                    break

        for element, element_desc in meaningful_elements:
            # Use professional validation pattern
            validate_pattern = self.professional_patterns["validate_element"][0].format(element=element_desc)
            scenario += f"    {validate_pattern}\n"

            # Add interaction with professional assertion
            scenario += f"    When I tap on \"{element_desc}\" element\n"
            assert_pattern = self.professional_patterns["assert_match"][0].format(
                field="interface response", expected="successful interaction"
            )
            scenario += f"    {assert_pattern}\n"

        # Check for duplicates
        if self._is_scenario_duplicate(scenario):
            return None

        return scenario

    def _generate_navigation_scenario(
        self, feature_name: str, clickable_elements: pd.DataFrame
    ) -> Optional[str]:
        """Generate executable navigation test scenario"""
        scenario_title = f"Navigate through {feature_name}"

        scenario = f"""
  Scenario: {scenario_title}
    Given I am on the {feature_name} screen
    {self._generate_executable_step("launch_app")}
    {self._generate_executable_step("verify_screen", screen=feature_name)}
"""

        # Filter and limit elements to prevent massive scenarios
        meaningful_elements = []
        for _, element in clickable_elements.iterrows():
            element_desc = self._get_element_description(element)

            # Skip generic or meaningless descriptions
            if (element_desc and
                element_desc != "the UI element" and
                "unknown" not in element_desc.lower() and
                len(element_desc) > 10):  # Ensure meaningful description

                meaningful_elements.append((element, element_desc))

        # Limit to maximum 5 elements for better test execution
        meaningful_elements = meaningful_elements[:5]

        # Generate executable steps for meaningful elements only
        for element, element_desc in meaningful_elements:
            # Verify element is visible
            verify_step = self._generate_executable_step("verify_element", element=element_desc)
            if verify_step:
                scenario += f"    {verify_step}\n"

            # Add tap action if element has meaningful text/content
            if (element.get('text') and str(element.get('text')).strip()) or \
               (element.get('content_desc') and str(element.get('content_desc')).strip()):
                tap_step = self._generate_executable_step("tap_element", element=element_desc)
                if tap_step:
                    scenario += f"    {tap_step}\n"
                    scenario += "    Then the application should respond to the tap\n"

        # Check for duplicates
        if self._is_scenario_duplicate(scenario):
            return None

        return scenario

    def _generate_professional_input_scenario(
        self, feature_name: str, input_elements: pd.DataFrame
    ) -> Optional[str]:
        """Generate professional-style input test scenario"""
        scenario_title = f"Input validation and data entry in {feature_name}"

        # Use professional patterns
        auth_step = self.professional_patterns["setup_authentication"][1]
        nav_step = self.professional_patterns["navigate_to_screen"][0].format(screen_name=feature_name)
        verify_step = self.professional_patterns["verify_response"][1]

        scenario = f"""
  Scenario: {scenario_title}
    {auth_step}
    {nav_step}
    {verify_step}
"""

        # Limit to maximum 3 input elements for better test execution
        limited_elements = input_elements.head(3)
        meaningful_inputs = []

        for _, element in limited_elements.iterrows():
            element_desc = self._get_element_description(element)
            if element_desc and element_desc != "the UI element":
                meaningful_inputs.append((element, element_desc))

        if not meaningful_inputs:
            return None  # No meaningful inputs found

        for element, element_desc in meaningful_inputs:
            # Use professional patterns for input testing
            scenario += f"    When I enter \"TEST_DATA_VALUE\" in \"{element_desc}\" field\n"

            # Add data collection pattern
            collect_pattern = self.professional_patterns["collect_data"][0].format(
                field=f"{element_desc}.value", variable="INPUT_DATA"
            )
            scenario += f"    {collect_pattern}\n"

            # Add assertion pattern
            assert_pattern = self.professional_patterns["assert_match"][0].format(
                field="input.value", expected="TEST_DATA_VALUE"
            )
            scenario += f"    {assert_pattern}\n"

        # Check for duplicates
        if self._is_scenario_duplicate(scenario):
            return None

        return scenario

    def _generate_input_scenario(
        self, feature_name: str, input_elements: pd.DataFrame
    ) -> Optional[str]:
        """Generate executable input test scenario"""
        scenario_title = f"Input data in {feature_name}"

        scenario = f"""
  Scenario: {scenario_title}
    Given I am on the {feature_name} screen
    {self._generate_executable_step("launch_app")}
    {self._generate_executable_step("verify_screen", screen=feature_name)}
"""

        # Limit to maximum 3 input elements for better test execution
        limited_elements = input_elements.head(3)

        meaningful_inputs = []
        for _, element in limited_elements.iterrows():
            element_desc = self._get_element_description(element)
            if element_desc and element_desc != "the UI element":
                meaningful_inputs.append((element, element_desc))

        if not meaningful_inputs:
            return None  # No meaningful inputs found

        for element, element_desc in meaningful_inputs:
            # Generate executable input step
            input_step = self._generate_executable_step("enter_text", text="test data", element=element_desc)
            if input_step:
                scenario += f"    {input_step}\n"
                scenario += f"    Then I should see the text in {element_desc}\n"

        # Check for duplicates
        if self._is_scenario_duplicate(scenario):
            return None

        return scenario

    def _generate_scroll_scenario(
        self, feature_name: str, scrollable_elements: pd.DataFrame
    ) -> str:
        """Generate scroll test scenario"""
        scenario = f"""
  Scenario: Scroll functionality in {feature_name}
    Given I am on the {feature_name} screen
    When I launch the mobile application
    Then I should see scrollable content
"""

        # Limit to maximum 3 scrollable elements to prevent huge scenarios
        meaningful_elements = []
        for _, element in scrollable_elements.iterrows():
            element_desc = self._get_element_description(element)
            if (element_desc and
                element_desc != "the UI element" and
                "unknown" not in element_desc.lower()):
                meaningful_elements.append((element, element_desc))
                if len(meaningful_elements) >= 3:  # Limit to 3 elements
                    break

        # If no meaningful elements found, use generic scroll actions
        if not meaningful_elements:
            scenario += "    When I scroll down on the screen\n"
            scenario += "    Then the content should scroll\n"
            scenario += "    When I scroll up on the screen\n"
            scenario += "    Then the content should scroll back\n"
        else:
            for element, element_desc in meaningful_elements:
                scenario += f"    When I scroll down on {element_desc}\n"
                scenario += "    Then the content should scroll\n"
                scenario += f"    When I scroll up on {element_desc}\n"
                scenario += "    Then the content should scroll back\n"

        return scenario

    def _generate_validation_scenario(
        self, feature_name: str, feature_data: pd.DataFrame
    ) -> str:
        """Generate UI validation scenario"""
        scenario = f"""
  Scenario: Validate {feature_name} UI elements
    Given I am on the {feature_name} screen
    When I launch the mobile application
    Then I should validate all UI elements
"""

        # Get unique element types if class_name column exists
        if 'class_name' in feature_data.columns:
            try:
                element_types = (
                    feature_data['class_name'].str.split('.').str[-1].unique()
                )
                
                for element_type in element_types:
                    if pd.notna(element_type) and element_type:
                        scenario += f"    And I should see {element_type} elements\n"
            except Exception as e:
                logger.warning(f"Error processing class_name column: {e}")
                # Fallback to generic validation
                scenario += f"    And I should see UI elements on the screen\n"
        elif 'element_type' in feature_data.columns:
            # Fallback to element_type column if available
            try:
                element_types = feature_data['element_type'].unique()
                
                for element_type in element_types:
                    if pd.notna(element_type) and element_type:
                        scenario += f"    And I should see {element_type} elements\n"
            except Exception as e:
                logger.warning(f"Error processing element_type column: {e}")
                # Fallback to generic validation
                scenario += f"    And I should see UI elements on the screen\n"
        else:
            # Generic validation if no specific element type information is available
            scenario += f"    And I should see all UI elements on the screen\n"

        # Check for text elements if text column exists
        if 'text' in feature_data.columns:
            try:
                text_elements = feature_data[
                    feature_data['text'].notna() & (feature_data['text'] != '')
                ]
                if not text_elements.empty:
                    scenario += "    And I should see text content\n"
            except Exception as e:
                logger.warning(f"Error processing text column: {e}")
        elif 'content-desc' in feature_data.columns:
            # Try content-desc as alternative
            try:
                text_elements = feature_data[
                    feature_data['content-desc'].notna() & (feature_data['content-desc'] != '')
                ]
                if not text_elements.empty:
                    scenario += "    And I should see descriptive content\n"
            except Exception as e:
                logger.warning(f"Error processing content-desc column: {e}")
        elif 'details' in feature_data.columns:
            # Try details as another alternative
            try:
                text_elements = feature_data[
                    feature_data['details'].notna() & (feature_data['details'] != '')
                ]
                if not text_elements.empty:
                    scenario += "    And I should see detailed content\n"
            except Exception as e:
                logger.warning(f"Error processing details column: {e}")

        return scenario

    def _generate_negative_scenarios(
        self, feature_name: str, feature_data: pd.DataFrame,
        clickable_elements: pd.DataFrame, input_elements: pd.DataFrame
    ) -> List[str]:
        """Generate negative test scenarios for comprehensive testing"""
        negative_scenarios = []

        try:
            # Generate invalid input scenario
            if not input_elements.empty:
                invalid_input_scenario = self._generate_invalid_input_scenario(
                    feature_name, input_elements
                )
                negative_scenarios.append(invalid_input_scenario)

            # Generate error handling scenario
            if not clickable_elements.empty:
                error_handling_scenario = self._generate_error_handling_scenario(
                    feature_name, clickable_elements
                )
                negative_scenarios.append(error_handling_scenario)

            # Generate boundary condition scenario
            boundary_scenario = self._generate_boundary_scenario(
                feature_name, feature_data
            )
            negative_scenarios.append(boundary_scenario)

        except Exception as e:
            logger.error(f"Failed to generate negative scenarios: {e}")

        return negative_scenarios

    def _generate_invalid_input_scenario(
        self, feature_name: str, input_elements: pd.DataFrame
    ) -> str:
        """Generate invalid input test scenario"""
        scenario = f"""
  Scenario: Handle invalid input in {feature_name}
    Given I am on the {feature_name} screen
    When I launch the mobile application
    Then I should see input fields
"""

        # Limit to maximum 3 input elements for negative testing
        limited_elements = input_elements.head(3)

        for _, element in limited_elements.iterrows():
            element_desc = self._get_element_description(element)
            if element_desc and element_desc != "the UI element":
                scenario += f"    When I enter invalid data \"@#$%^&*()\" in {element_desc}\n"
                scenario += "    Then the application should handle the invalid input gracefully\n"
                scenario += "    And I should see appropriate error message or validation\n"

        return scenario

    def _generate_error_handling_scenario(
        self, feature_name: str, clickable_elements: pd.DataFrame
    ) -> str:
        """Generate error handling test scenario"""
        scenario = f"""
  Scenario: Error handling in {feature_name}
    Given I am on the {feature_name} screen
    When I launch the mobile application
    Then I should see the {feature_name} interface
"""

        # Take first few meaningful clickable elements for error testing
        meaningful_elements = []
        for _, element in clickable_elements.iterrows():
            element_desc = self._get_element_description(element)
            if (element_desc and
                element_desc != "the UI element" and
                "unknown" not in element_desc.lower()):
                meaningful_elements.append((element, element_desc))
                if len(meaningful_elements) >= 2:  # Limit to 2 elements
                    break

        for element, element_desc in meaningful_elements:
            scenario += f"    When I rapidly tap {element_desc} multiple times\n"
            scenario += "    Then the application should not crash or freeze\n"
            scenario += "    And the interface should remain responsive\n"

        return scenario

    def _generate_boundary_scenario(
        self, feature_name: str, feature_data: pd.DataFrame
    ) -> str:
        """Generate boundary condition test scenario"""
        scenario = f"""
  Scenario: Boundary conditions in {feature_name}
    Given I am on the {feature_name} screen
    When I launch the mobile application
    Then I should see the {feature_name} interface
    When I test edge cases and boundary conditions
    Then the application should handle them gracefully
    And no unexpected errors should occur
    And the UI should remain stable
"""

        return scenario

    # ========== COMPREHENSIVE SCENARIO GENERATION METHODS ==========

    async def _generate_comprehensive_positive_scenarios(
        self, feature_name: str, feature_data: pd.DataFrame,
        clickable_elements: pd.DataFrame, input_elements: pd.DataFrame,
        scrollable_elements: pd.DataFrame, enhanced_context: Dict[str, Any]
    ) -> List[str]:
        """Generate 100% comprehensive positive test scenarios covering ALL positive testing categories"""
        positive_scenarios = []

        try:
            console.print(f"[cyan]📋 Generating 100% comprehensive positive scenarios for {feature_name}[/cyan]")

            # CATEGORY 1: CORE FUNCTIONALITY TESTING (3-4 scenarios)
            core_scenarios = await self._generate_core_functionality_scenarios(
                feature_name, clickable_elements, input_elements
            )
            positive_scenarios.extend(core_scenarios)

            # CATEGORY 2: NAVIGATION & USER FLOW TESTING (3-4 scenarios)
            navigation_scenarios = await self._generate_navigation_flow_scenarios(
                feature_name, clickable_elements, enhanced_context
            )
            positive_scenarios.extend(navigation_scenarios)

            # CATEGORY 3: DATA INPUT & VALIDATION TESTING (2-3 scenarios)
            if not input_elements.empty:
                data_scenarios = await self._generate_data_input_scenarios(
                    feature_name, input_elements
                )
                positive_scenarios.extend(data_scenarios)

            # CATEGORY 4: UI INTERACTION & RESPONSIVENESS TESTING (2-3 scenarios)
            interaction_scenarios = await self._generate_ui_interaction_scenarios(
                feature_name, clickable_elements, scrollable_elements
            )
            positive_scenarios.extend(interaction_scenarios)

            # CATEGORY 5: CONTENT VERIFICATION & DISPLAY TESTING (2 scenarios)
            content_scenarios = await self._generate_content_verification_scenarios(
                feature_name, feature_data
            )
            positive_scenarios.extend(content_scenarios)

            # CATEGORY 6: ACCESSIBILITY & USABILITY TESTING (2 scenarios)
            accessibility_scenarios = await self._generate_accessibility_scenarios(
                feature_name, clickable_elements, input_elements
            )
            positive_scenarios.extend(accessibility_scenarios)

            # CATEGORY 7: PERFORMANCE & RESPONSIVENESS TESTING (1-2 scenarios)
            performance_scenarios = await self._generate_performance_scenarios(
                feature_name, clickable_elements
            )
            positive_scenarios.extend(performance_scenarios)

            # CATEGORY 8: INTEGRATION & WORKFLOW TESTING (1-2 scenarios)
            workflow_scenarios = await self._generate_workflow_scenarios(
                feature_name, clickable_elements, input_elements
            )
            positive_scenarios.extend(workflow_scenarios)

            console.print(f"[green]✅ Generated {len(positive_scenarios)} comprehensive positive scenarios across 8 categories[/green]")
            return positive_scenarios

        except Exception as e:
            logger.error(f"Failed to generate comprehensive positive scenarios: {e}")
            return []

    # ========== COMPREHENSIVE POSITIVE SCENARIO CATEGORIES ==========

    async def _generate_core_functionality_scenarios(
        self, feature_name: str, clickable_elements: pd.DataFrame, input_elements: pd.DataFrame
    ) -> List[str]:
        """Generate core functionality testing scenarios"""
        scenarios = []

        # Basic feature access and functionality
        scenarios.append(f"""
  Scenario: Core functionality access in {feature_name}
    Given I launch the "Rumah Pendidikan" application
    When I navigate to the "{feature_name}" section
    Then I should see the "{feature_name}" interface loaded
    And all main elements should be visible and accessible
    And the feature should be ready for user interaction
""")

        # Feature-specific core operations
        scenarios.append(f"""
  Scenario: Primary operations in {feature_name}
    Given I launch the "Rumah Pendidikan" application
    When I navigate to the "{feature_name}" section
    And I perform the main operations available in this feature
    Then all operations should execute successfully
    And the results should be displayed correctly
    And the feature should maintain its functionality
""")

        # Feature responsiveness and stability
        scenarios.append(f"""
  Scenario: Feature responsiveness and stability in {feature_name}
    Given I launch the "Rumah Pendidikan" application
    When I navigate to the "{feature_name}" section
    And I interact with various elements in the feature
    Then the feature should respond promptly to all interactions
    And the interface should remain stable throughout usage
    And no unexpected behaviors should occur
""")

        return scenarios

    async def _generate_navigation_flow_scenarios(
        self, feature_name: str, clickable_elements: pd.DataFrame, enhanced_context: Dict[str, Any]
    ) -> List[str]:
        """Generate navigation and user flow testing scenarios"""
        scenarios = []

        # Smooth navigation flow
        scenarios.append(f"""
  Scenario: Smooth navigation flow in {feature_name}
    Given I launch the "Rumah Pendidikan" application
    When I navigate to the "{feature_name}" section
    And I explore different areas within the feature
    And I navigate back and forth between sections
    Then all navigation should be smooth and intuitive
    And I should always know where I am in the application
    And navigation controls should work consistently
""")

        # Deep navigation and return
        scenarios.append(f"""
  Scenario: Deep navigation and return flow in {feature_name}
    Given I launch the "Rumah Pendidikan" application
    When I navigate to the "{feature_name}" section
    And I go deeper into sub-sections and detailed views
    And I use back navigation to return to previous screens
    Then I should be able to navigate to any depth
    And return navigation should work correctly
    And my navigation history should be preserved
""")

        # Cross-feature navigation
        scenarios.append(f"""
  Scenario: Cross-feature navigation from {feature_name}
    Given I launch the "Rumah Pendidikan" application
    When I navigate to the "{feature_name}" section
    And I access links or references to other features
    And I navigate to related features and return
    Then cross-feature navigation should work seamlessly
    And I should be able to return to "{feature_name}" easily
    And context should be preserved during navigation
""")

        return scenarios

    async def _generate_data_input_scenarios(
        self, feature_name: str, input_elements: pd.DataFrame
    ) -> List[str]:
        """Generate data input and validation testing scenarios"""
        scenarios = []

        # Valid data input
        scenarios.append(f"""
  Scenario: Valid data input in {feature_name}
    Given I launch the "Rumah Pendidikan" application
    When I navigate to the "{feature_name}" section
    And I enter valid data in all available input fields
    And I submit or save the entered data
    Then all data should be accepted and processed correctly
    And appropriate confirmation should be displayed
    And the data should be stored or used as expected
""")

        # Data format validation
        scenarios.append(f"""
  Scenario: Data format validation in {feature_name}
    Given I launch the "Rumah Pendidikan" application
    When I navigate to the "{feature_name}" section
    And I enter data in various formats (text, numbers, dates)
    And I test different input patterns and lengths
    Then the application should validate data formats appropriately
    And provide helpful guidance for correct input
    And accept all valid data formats
""")

        return scenarios

    async def _generate_ui_interaction_scenarios(
        self, feature_name: str, clickable_elements: pd.DataFrame, scrollable_elements: pd.DataFrame
    ) -> List[str]:
        """Generate UI interaction and responsiveness testing scenarios"""
        scenarios = []

        # Interactive element responsiveness
        scenarios.append(f"""
  Scenario: Interactive element responsiveness in {feature_name}
    Given I launch the "Rumah Pendidikan" application
    When I navigate to the "{feature_name}" section
    And I interact with buttons, links, and interactive elements
    Then all elements should respond immediately to touch
    And visual feedback should be provided for interactions
    And the interface should feel responsive and smooth
""")

        # Scroll and gesture interactions
        if not scrollable_elements.empty:
            scenarios.append(f"""
  Scenario: Scroll and gesture interactions in {feature_name}
    Given I launch the "Rumah Pendidikan" application
    When I navigate to the "{feature_name}" section
    And I perform various scroll gestures (up, down, fling)
    And I test different touch patterns and gestures
    Then all scroll operations should work smoothly
    And gesture recognition should be accurate
    And content should scroll naturally and responsively
""")

        return scenarios

    async def _generate_content_verification_scenarios(
        self, feature_name: str, feature_data: pd.DataFrame
    ) -> List[str]:
        """Generate content verification and display testing scenarios"""
        scenarios = []

        # Content accuracy and completeness
        scenarios.append(f"""
  Scenario: Content accuracy and completeness in {feature_name}
    Given I launch the "Rumah Pendidikan" application
    When I navigate to the "{feature_name}" section
    And I review all displayed content and information
    Then all content should be accurate and up-to-date
    And information should be complete and well-organized
    And text should be clear and readable
""")

        # Content layout and presentation
        scenarios.append(f"""
  Scenario: Content layout and presentation in {feature_name}
    Given I launch the "Rumah Pendidikan" application
    When I navigate to the "{feature_name}" section
    And I observe the layout and presentation of content
    Then content should be well-organized and visually appealing
    And layout should be consistent with design standards
    And information hierarchy should be clear and logical
""")

        return scenarios

    async def _generate_accessibility_scenarios(
        self, feature_name: str, clickable_elements: pd.DataFrame, input_elements: pd.DataFrame
    ) -> List[str]:
        """Generate accessibility and usability testing scenarios"""
        scenarios = []

        # Accessibility features
        scenarios.append(f"""
  Scenario: Accessibility features in {feature_name}
    Given I launch the "Rumah Pendidikan" application
    When I navigate to the "{feature_name}" section
    And I test accessibility features like text size adjustment
    And I verify screen reader compatibility where available
    Then the feature should be accessible to users with different needs
    And accessibility options should work correctly
    And the interface should remain usable with accessibility features enabled
""")

        # Usability and user experience
        scenarios.append(f"""
  Scenario: Usability and user experience in {feature_name}
    Given I launch the "Rumah Pendidikan" application
    When I navigate to the "{feature_name}" section
    And I use the feature as a typical user would
    Then the feature should be intuitive and easy to use
    And common tasks should be straightforward to complete
    And the user experience should be pleasant and efficient
""")

        return scenarios

    async def _generate_performance_scenarios(
        self, feature_name: str, clickable_elements: pd.DataFrame
    ) -> List[str]:
        """Generate performance and responsiveness testing scenarios"""
        scenarios = []

        # Performance under normal usage
        scenarios.append(f"""
  Scenario: Performance under normal usage in {feature_name}
    Given I launch the "Rumah Pendidikan" application
    When I navigate to the "{feature_name}" section
    And I use the feature normally for an extended period
    Then the feature should maintain good performance
    And response times should remain acceptable
    And memory usage should be efficient
""")

        return scenarios

    async def _generate_workflow_scenarios(
        self, feature_name: str, clickable_elements: pd.DataFrame, input_elements: pd.DataFrame
    ) -> List[str]:
        """Generate integration and workflow testing scenarios"""
        scenarios = []

        # End-to-end workflow
        scenarios.append(f"""
  Scenario: Complete workflow in {feature_name}
    Given I launch the "Rumah Pendidikan" application
    When I navigate to the "{feature_name}" section
    And I complete a typical user workflow from start to finish
    Then the entire workflow should execute smoothly
    And all steps should integrate properly
    And the final result should meet user expectations
""")

        return scenarios

    async def _generate_comprehensive_negative_scenarios(
        self, feature_name: str, feature_data: pd.DataFrame,
        clickable_elements: pd.DataFrame, input_elements: pd.DataFrame
    ) -> List[str]:
        """Generate comprehensive negative test scenarios"""
        negative_scenarios = []

        try:
            # 1. Invalid Input Scenario
            if not input_elements.empty:
                invalid_input_scenario = self._generate_invalid_input_scenario(
                    feature_name, input_elements
                )
                negative_scenarios.append(invalid_input_scenario)

            # 2. Enhanced Error Handling Scenario
            if not clickable_elements.empty:
                error_handling_scenario = self._generate_enhanced_error_handling_scenario(
                    feature_name, clickable_elements
                )
                negative_scenarios.append(error_handling_scenario)

            # 3. Boundary Condition Scenario
            boundary_scenario = self._generate_boundary_scenario(
                feature_name, feature_data
            )
            negative_scenarios.append(boundary_scenario)

            # 4. Stress Testing Scenario
            if not clickable_elements.empty:
                stress_scenario = self._generate_stress_testing_scenario(
                    feature_name, clickable_elements
                )
                negative_scenarios.append(stress_scenario)

            # 5. Network Error Simulation Scenario
            network_error_scenario = self._generate_network_error_scenario(
                feature_name
            )
            negative_scenarios.append(network_error_scenario)

            # 6. Permission Denied Scenario
            permission_scenario = self._generate_permission_denied_scenario(
                feature_name
            )
            negative_scenarios.append(permission_scenario)

        except Exception as e:
            logger.error(f"Failed to generate negative scenarios: {e}")

        return negative_scenarios

    async def _generate_enhanced_negative_scenarios(
        self, feature_name: str, feature_data: pd.DataFrame,
        clickable_elements: pd.DataFrame, input_elements: pd.DataFrame,
        positive_count: int
    ) -> List[str]:
        """Generate comprehensive negative scenarios ensuring 100% negative coverage across all testing types"""
        negative_scenarios = []

        try:
            # Calculate target for COMPREHENSIVE negative coverage
            # Target: 2x positive count for 100% comprehensive coverage, minimum 15, maximum 40 per feature
            target_negative_count = min(max(int(positive_count * 2.0), 15), 40)

            console.print(f"[cyan]🎯 Generating {target_negative_count} comprehensive negative scenarios for {feature_name} (positive: {positive_count})[/cyan]")

            # 1. Core Negative Scenarios (Always generate these)
            core_negative_scenarios = await self._generate_core_negative_scenarios(
                feature_name, feature_data, clickable_elements, input_elements
            )
            negative_scenarios.extend(core_negative_scenarios)

            # 2. Comprehensive Negative Categories for 100% Coverage
            comprehensive_scenarios = await self._generate_comprehensive_negative_categories(
                feature_name, feature_data, clickable_elements, input_elements
            )
            negative_scenarios.extend(comprehensive_scenarios)

            # 3. Additional Negative Scenarios to reach target count
            additional_needed = target_negative_count - len(negative_scenarios)
            if additional_needed > 0:
                additional_scenarios = await self._generate_additional_negative_scenarios(
                    feature_name, feature_data, clickable_elements, input_elements, additional_needed
                )
                negative_scenarios.extend(additional_scenarios)

            # 3. Ensure we have enough negative scenarios
            while len(negative_scenarios) < target_negative_count:
                extra_scenario = self._generate_extra_negative_scenario(
                    feature_name, len(negative_scenarios)
                )
                negative_scenarios.append(extra_scenario)

            console.print(f"[green]✅ Generated {len(negative_scenarios)} negative scenarios for {feature_name}[/green]")

        except Exception as e:
            logger.error(f"Failed to generate enhanced negative scenarios: {e}")
            # Fallback to basic negative scenarios
            negative_scenarios = await self._generate_comprehensive_negative_scenarios(
                feature_name, feature_data, clickable_elements, input_elements
            )

        return negative_scenarios

    async def _generate_core_negative_scenarios(
        self, feature_name: str, feature_data: pd.DataFrame,
        clickable_elements: pd.DataFrame, input_elements: pd.DataFrame
    ) -> List[str]:
        """Generate core negative scenarios that should always be included"""
        core_scenarios = []

        try:
            # 1. Invalid Input Scenario
            if not input_elements.empty:
                invalid_input_scenario = self._generate_invalid_input_scenario(
                    feature_name, input_elements
                )
                core_scenarios.append(invalid_input_scenario)

            # 2. Error Handling Scenario
            error_handling_scenario = self._generate_error_handling_scenario(
                feature_name, clickable_elements
            )
            core_scenarios.append(error_handling_scenario)

            # 3. Boundary Condition Scenario
            boundary_scenario = self._generate_boundary_scenario(
                feature_name, feature_data
            )
            core_scenarios.append(boundary_scenario)

            # 4. Network Error Scenario
            network_error_scenario = self._generate_network_error_scenario(
                feature_name
            )
            core_scenarios.append(network_error_scenario)

            # 5. Permission Denied Scenario
            permission_scenario = self._generate_permission_denied_scenario(
                feature_name
            )
            core_scenarios.append(permission_scenario)

        except Exception as e:
            logger.error(f"Failed to generate core negative scenarios: {e}")

        return core_scenarios

    async def _generate_comprehensive_negative_categories(
        self, feature_name: str, feature_data: pd.DataFrame,
        clickable_elements: pd.DataFrame, input_elements: pd.DataFrame
    ) -> List[str]:
        """Generate comprehensive negative scenarios covering ALL negative testing categories"""
        comprehensive_scenarios = []

        try:
            console.print(f"[cyan]📋 Generating comprehensive negative categories for {feature_name}[/cyan]")

            # Category 1: Input Validation & Data Integrity (5 scenarios)
            input_validation_scenarios = [
                self._generate_mobile_invalid_input_scenario(feature_name, "empty fields"),
                self._generate_mobile_invalid_input_scenario(feature_name, "special characters"),
                self._generate_mobile_invalid_input_scenario(feature_name, "maximum length exceeded"),
                self._generate_mobile_invalid_input_scenario(feature_name, "invalid format"),
                self._generate_mobile_data_error_scenario(feature_name, "corrupted data")
            ]
            comprehensive_scenarios.extend(input_validation_scenarios)

            # Category 2: Navigation & UI Errors (4 scenarios)
            navigation_scenarios = [
                self._generate_mobile_navigation_error_scenario(feature_name, "broken link"),
                self._generate_mobile_navigation_error_scenario(feature_name, "missing page"),
                self._generate_mobile_navigation_error_scenario(feature_name, "access denied"),
                self._generate_mobile_interaction_error_scenario(feature_name, "rapid tapping")
            ]
            comprehensive_scenarios.extend(navigation_scenarios)

            # Category 3: Network & Connectivity (4 scenarios)
            network_scenarios = [
                self._generate_mobile_network_error_scenario(feature_name, "connection timeout"),
                self._generate_mobile_network_error_scenario(feature_name, "slow connection"),
                self._generate_mobile_network_error_scenario(feature_name, "connection lost"),
                self._generate_mobile_data_error_scenario(feature_name, "sync failure")
            ]
            comprehensive_scenarios.extend(network_scenarios)

            # Category 4: Device & System Limitations (4 scenarios)
            device_scenarios = [
                self._generate_mobile_device_error_scenario(feature_name, "low memory"),
                self._generate_mobile_device_error_scenario(feature_name, "storage full"),
                self._generate_mobile_device_error_scenario(feature_name, "battery low"),
                self._generate_mobile_interaction_error_scenario(feature_name, "screen rotation")
            ]
            comprehensive_scenarios.extend(device_scenarios)

            # Category 5: Security & Permissions (4 scenarios)
            security_scenarios = [
                self._generate_mobile_permission_error_scenario(feature_name, "camera access"),
                self._generate_mobile_permission_error_scenario(feature_name, "location access"),
                self._generate_mobile_permission_error_scenario(feature_name, "storage access"),
                self._generate_security_breach_scenario(feature_name)
            ]
            comprehensive_scenarios.extend(security_scenarios)

            # Category 6: Performance & Load (3 scenarios)
            performance_scenarios = [
                self._generate_performance_degradation_scenario(feature_name),
                self._generate_concurrent_usage_scenario(feature_name),
                self._generate_resource_exhaustion_scenario(feature_name)
            ]
            comprehensive_scenarios.extend(performance_scenarios)

            # Category 7: Edge Cases & Boundary Conditions (3 scenarios)
            edge_case_scenarios = [
                self._generate_boundary_condition_scenario(feature_name),
                self._generate_extreme_data_scenario(feature_name),
                self._generate_state_transition_error_scenario(feature_name)
            ]
            comprehensive_scenarios.extend(edge_case_scenarios)

            # Category 8: Recovery & Resilience (3 scenarios)
            recovery_scenarios = [
                self._generate_crash_recovery_scenario(feature_name),
                self._generate_data_recovery_scenario(feature_name),
                self._generate_session_recovery_scenario(feature_name)
            ]
            comprehensive_scenarios.extend(recovery_scenarios)

            console.print(f"[green]✅ Generated {len(comprehensive_scenarios)} comprehensive negative scenarios across 8 categories[/green]")
            return comprehensive_scenarios

        except Exception as e:
            logger.error(f"Failed to generate comprehensive negative categories: {e}")
            return []

    async def _generate_additional_negative_scenarios(
        self, feature_name: str, feature_data: pd.DataFrame,
        clickable_elements: pd.DataFrame, input_elements: pd.DataFrame,
        count_needed: int
    ) -> List[str]:
        """Generate additional mobile UI-focused negative scenarios to reach target count"""
        additional_scenarios = []

        try:
            # Mobile UI-focused negative scenario generators
            scenario_generators = [
                # Input validation errors
                lambda: self._generate_mobile_invalid_input_scenario(feature_name, "empty fields"),
                lambda: self._generate_mobile_invalid_input_scenario(feature_name, "special characters"),
                lambda: self._generate_mobile_invalid_input_scenario(feature_name, "maximum length exceeded"),
                lambda: self._generate_mobile_invalid_input_scenario(feature_name, "invalid format"),

                # Navigation and UI errors
                lambda: self._generate_mobile_navigation_error_scenario(feature_name, "broken link"),
                lambda: self._generate_mobile_navigation_error_scenario(feature_name, "missing page"),
                lambda: self._generate_mobile_navigation_error_scenario(feature_name, "access denied"),

                # Network and connectivity errors
                lambda: self._generate_mobile_network_error_scenario(feature_name, "connection timeout"),
                lambda: self._generate_mobile_network_error_scenario(feature_name, "slow connection"),
                lambda: self._generate_mobile_network_error_scenario(feature_name, "connection lost"),

                # Device and system errors
                lambda: self._generate_mobile_device_error_scenario(feature_name, "low memory"),
                lambda: self._generate_mobile_device_error_scenario(feature_name, "storage full"),
                lambda: self._generate_mobile_device_error_scenario(feature_name, "battery low"),

                # User interaction errors
                lambda: self._generate_mobile_interaction_error_scenario(feature_name, "rapid tapping"),
                lambda: self._generate_mobile_interaction_error_scenario(feature_name, "gesture conflict"),
                lambda: self._generate_mobile_interaction_error_scenario(feature_name, "screen rotation"),

                # Data and content errors
                lambda: self._generate_mobile_data_error_scenario(feature_name, "corrupted data"),
                lambda: self._generate_mobile_data_error_scenario(feature_name, "missing content"),
                lambda: self._generate_mobile_data_error_scenario(feature_name, "sync failure"),

                # Permission and security errors
                lambda: self._generate_mobile_permission_error_scenario(feature_name, "camera access"),
                lambda: self._generate_mobile_permission_error_scenario(feature_name, "location access"),
                lambda: self._generate_mobile_permission_error_scenario(feature_name, "storage access"),
            ]

            # Generate scenarios up to the needed count, cycling through generators if needed
            for i in range(count_needed):
                try:
                    generator_index = i % len(scenario_generators)
                    scenario = scenario_generators[generator_index]()
                    additional_scenarios.append(scenario)
                except Exception as e:
                    logger.error(f"Failed to generate additional negative scenario {i}: {e}")
                    # Fallback to basic negative scenario
                    fallback_scenario = self._generate_basic_negative_scenario(feature_name, i)
                    additional_scenarios.append(fallback_scenario)

        except Exception as e:
            logger.error(f"Failed to generate additional negative scenarios: {e}")

        return additional_scenarios

    def _generate_mobile_invalid_input_scenario(self, feature_name: str, error_type: str) -> str:
        """Generate mobile UI-focused invalid input scenario"""
        error_scenarios = {
            "empty fields": {
                "action": "leave all fields empty",
                "expected": "validation error messages",
                "validation": "required field indicators are shown"
            },
            "special characters": {
                "action": "enter special characters (!@#$%^&*)",
                "expected": "input validation error",
                "validation": "invalid character warning appears"
            },
            "maximum length exceeded": {
                "action": "enter text exceeding maximum length",
                "expected": "character limit warning",
                "validation": "input is truncated or blocked"
            },
            "invalid format": {
                "action": "enter invalid email format",
                "expected": "format validation error",
                "validation": "proper format example is shown"
            }
        }

        error_info = error_scenarios.get(error_type, error_scenarios["empty fields"])

        return f"""
  Scenario: Invalid input handling - {error_type} in {feature_name}
    Given I launch the "Rumah Pendidikan" application
    When I navigate to the "{feature_name}" section
    And I {error_info["action"]} in the input fields
    And I tap "Submit" or "Save" button
    Then I should see "{error_info["expected"]}"
    And I should see "{error_info["validation"]}"
    And the form should not be submitted
    And I should remain on the current screen
"""

    def _generate_mobile_navigation_error_scenario(self, feature_name: str, error_type: str) -> str:
        """Generate mobile UI-focused navigation error scenario"""
        error_scenarios = {
            "broken link": {
                "action": "tap on a broken or invalid link",
                "expected": "page not found error",
                "recovery": "back button should work"
            },
            "missing page": {
                "action": "try to access a non-existent page",
                "expected": "404 or missing content message",
                "recovery": "navigation menu should remain accessible"
            },
            "access denied": {
                "action": "try to access restricted content",
                "expected": "access denied message",
                "recovery": "login prompt should appear"
            }
        }

        error_info = error_scenarios.get(error_type, error_scenarios["broken link"])

        return f"""
  Scenario: Navigation error handling - {error_type} in {feature_name}
    Given I launch the "Rumah Pendidikan" application
    When I navigate to the "{feature_name}" section
    And I {error_info["action"]}
    Then I should see "{error_info["expected"]}"
    And {error_info["recovery"]}
    And the application should not crash
    And I should be able to continue using the app
"""

    def _generate_mobile_network_error_scenario(self, feature_name: str, error_type: str) -> str:
        """Generate mobile UI-focused network error scenario"""
        error_scenarios = {
            "connection timeout": {
                "condition": "network request times out",
                "expected": "connection timeout message",
                "action": "retry button should be available"
            },
            "slow connection": {
                "condition": "network connection is very slow",
                "expected": "loading indicator or progress bar",
                "action": "content should load eventually"
            },
            "connection lost": {
                "condition": "network connection is lost during operation",
                "expected": "connection lost message",
                "action": "offline mode should be activated"
            }
        }

        error_info = error_scenarios.get(error_type, error_scenarios["connection timeout"])

        return f"""
  Scenario: Network error handling - {error_type} in {feature_name}
    Given I launch the "Rumah Pendidikan" application
    When I navigate to the "{feature_name}" section
    And {error_info["condition"]} while loading content
    Then I should see "{error_info["expected"]}"
    And {error_info["action"]}
    And the application should handle the error gracefully
    And cached content should be shown if available
"""

    def _generate_mobile_device_error_scenario(self, feature_name: str, error_type: str) -> str:
        """Generate mobile UI-focused device error scenario"""
        error_scenarios = {
            "low memory": {
                "condition": "device memory is low",
                "expected": "low memory warning or app optimization",
                "action": "app should free up memory or show memory usage"
            },
            "storage full": {
                "condition": "device storage is full",
                "expected": "storage full message",
                "action": "storage cleanup options should be provided"
            },
            "battery low": {
                "condition": "device battery is critically low",
                "expected": "battery saving mode activation",
                "action": "non-essential features should be disabled"
            }
        }

        error_info = error_scenarios.get(error_type, error_scenarios["low memory"])

        return f"""
  Scenario: Device limitation handling - {error_type} in {feature_name}
    Given I launch the "Rumah Pendidikan" application
    When I navigate to the "{feature_name}" section
    And {error_info["condition"]} while using the feature
    Then I should see "{error_info["expected"]}"
    And {error_info["action"]}
    And the application should continue functioning
    And critical features should remain accessible
"""

    def _generate_mobile_interaction_error_scenario(self, feature_name: str, error_type: str) -> str:
        """Generate mobile UI-focused interaction error scenario"""
        error_scenarios = {
            "rapid tapping": {
                "action": "tap buttons rapidly multiple times",
                "expected": "duplicate action prevention",
                "validation": "only one action should be processed"
            },
            "gesture conflict": {
                "action": "perform conflicting gestures simultaneously",
                "expected": "gesture priority handling",
                "validation": "primary gesture should be recognized"
            },
            "screen rotation": {
                "action": "rotate screen during form input",
                "expected": "layout adjustment and data preservation",
                "validation": "input data should be retained"
            }
        }

        error_info = error_scenarios.get(error_type, error_scenarios["rapid tapping"])

        return f"""
  Scenario: User interaction error handling - {error_type} in {feature_name}
    Given I launch the "Rumah Pendidikan" application
    When I navigate to the "{feature_name}" section
    And I {error_info["action"]}
    Then I should see "{error_info["expected"]}"
    And {error_info["validation"]}
    And the interface should remain responsive
    And no duplicate actions should occur
"""

    def _generate_mobile_data_error_scenario(self, feature_name: str, error_type: str) -> str:
        """Generate mobile UI-focused data error scenario"""
        error_scenarios = {
            "corrupted data": {
                "condition": "data becomes corrupted during transfer",
                "expected": "data corruption error message",
                "action": "data refresh or reload option should be available"
            },
            "missing content": {
                "condition": "expected content is missing or unavailable",
                "expected": "missing content placeholder",
                "action": "alternative content or retry option should be shown"
            },
            "sync failure": {
                "condition": "data synchronization fails",
                "expected": "sync failure notification",
                "action": "manual sync option should be available"
            }
        }

        error_info = error_scenarios.get(error_type, error_scenarios["corrupted data"])

        return f"""
  Scenario: Data error handling - {error_type} in {feature_name}
    Given I launch the "Rumah Pendidikan" application
    When I navigate to the "{feature_name}" section
    And {error_info["condition"]}
    Then I should see "{error_info["expected"]}"
    And {error_info["action"]}
    And the application should handle the error gracefully
    And user should be able to recover from the error
"""

    def _generate_mobile_permission_error_scenario(self, feature_name: str, permission_type: str) -> str:
        """Generate mobile UI-focused permission error scenario"""
        permission_scenarios = {
            "camera access": {
                "action": "try to use camera feature without permission",
                "expected": "camera permission request or denial message",
                "recovery": "settings link to enable permission should be provided"
            },
            "location access": {
                "action": "try to use location feature without permission",
                "expected": "location permission request or denial message",
                "recovery": "manual location input option should be available"
            },
            "storage access": {
                "action": "try to save files without storage permission",
                "expected": "storage permission request or denial message",
                "recovery": "alternative save options should be provided"
            }
        }

        permission_info = permission_scenarios.get(permission_type, permission_scenarios["camera access"])

        return f"""
  Scenario: Permission error handling - {permission_type} in {feature_name}
    Given I launch the "Rumah Pendidikan" application
    When I navigate to the "{feature_name}" section
    And I {permission_info["action"]}
    Then I should see "{permission_info["expected"]}"
    And {permission_info["recovery"]}
    And the application should not crash
    And alternative workflows should be available
"""

    def _generate_security_breach_scenario(self, feature_name: str) -> str:
        """Generate security breach testing scenario"""
        return f"""
  Scenario: Security breach prevention in {feature_name}
    Given I launch the "Rumah Pendidikan" application
    When I navigate to the "{feature_name}" section
    And I attempt to access restricted functionality
    And I try to bypass authentication mechanisms
    Then I should see "Access denied" or security warning
    And unauthorized access should be prevented
    And security logs should record the attempt
    And the application should remain secure
"""

    def _generate_performance_degradation_scenario(self, feature_name: str) -> str:
        """Generate performance degradation testing scenario"""
        return f"""
  Scenario: Performance degradation handling in {feature_name}
    Given I launch the "Rumah Pendidikan" application
    When I navigate to the "{feature_name}" section
    And I perform intensive operations repeatedly
    And I stress test the feature with heavy usage
    Then the application should maintain acceptable performance
    And response times should remain reasonable
    And the interface should stay responsive
    And no performance-related crashes should occur
"""

    def _generate_concurrent_usage_scenario(self, feature_name: str) -> str:
        """Generate concurrent usage testing scenario"""
        return f"""
  Scenario: Concurrent usage handling in {feature_name}
    Given I launch the "Rumah Pendidikan" application
    When I navigate to the "{feature_name}" section
    And I perform multiple actions simultaneously
    And I interact with different elements at the same time
    Then the application should handle concurrent operations
    And no conflicts should occur between actions
    And data integrity should be maintained
    And the user experience should remain smooth
"""

    def _generate_boundary_condition_scenario(self, feature_name: str) -> str:
        """Generate boundary condition testing scenario"""
        return f"""
  Scenario: Boundary condition testing in {feature_name}
    Given I launch the "Rumah Pendidikan" application
    When I navigate to the "{feature_name}" section
    And I test with minimum and maximum input values
    And I push the feature to its operational limits
    Then the application should handle boundary conditions gracefully
    And appropriate limits should be enforced
    And error messages should guide users properly
    And the system should remain stable
"""

    def _generate_extreme_data_scenario(self, feature_name: str) -> str:
        """Generate extreme data testing scenario"""
        return f"""
  Scenario: Extreme data handling in {feature_name}
    Given I launch the "Rumah Pendidikan" application
    When I navigate to the "{feature_name}" section
    And I input extremely large amounts of data
    And I test with unusual data patterns
    Then the application should handle extreme data gracefully
    And performance should degrade gracefully if needed
    And data validation should work correctly
    And no data corruption should occur
"""

    def _generate_state_transition_error_scenario(self, feature_name: str) -> str:
        """Generate state transition error testing scenario"""
        return f"""
  Scenario: State transition error handling in {feature_name}
    Given I launch the "Rumah Pendidikan" application
    When I navigate to the "{feature_name}" section
    And I trigger invalid state transitions
    And I attempt to access features in wrong states
    Then the application should prevent invalid transitions
    And appropriate error messages should be shown
    And the system should return to a valid state
    And no inconsistent states should persist
"""

    def _generate_crash_recovery_scenario(self, feature_name: str) -> str:
        """Generate crash recovery testing scenario"""
        return f"""
  Scenario: Crash recovery in {feature_name}
    Given I launch the "Rumah Pendidikan" application
    When I navigate to the "{feature_name}" section
    And I simulate application crash conditions
    And I restart the application after crash
    Then the application should recover gracefully
    And user data should be preserved
    And the feature should remain functional
    And crash logs should be generated for debugging
"""

    def _generate_data_recovery_scenario(self, feature_name: str) -> str:
        """Generate data recovery testing scenario"""
        return f"""
  Scenario: Data recovery in {feature_name}
    Given I launch the "Rumah Pendidikan" application
    When I navigate to the "{feature_name}" section
    And I experience data loss or corruption
    And I trigger data recovery mechanisms
    Then the application should attempt data recovery
    And backup data should be restored if available
    And users should be informed of recovery status
    And data integrity should be verified
"""

    def _generate_session_recovery_scenario(self, feature_name: str) -> str:
        """Generate session recovery testing scenario"""
        return f"""
  Scenario: Session recovery in {feature_name}
    Given I launch the "Rumah Pendidikan" application
    When I navigate to the "{feature_name}" section
    And my session expires or gets interrupted
    And I attempt to continue using the feature
    Then the application should handle session recovery
    And re-authentication should be prompted if needed
    And user progress should be preserved where possible
    And the recovery process should be seamless
"""

    def _generate_basic_negative_scenario(self, feature_name: str, scenario_index: int) -> str:
        """Generate basic negative scenario as fallback"""
        basic_scenarios = [
            "Invalid input validation",
            "Error message display",
            "Boundary condition testing",
            "Network error handling",
            "Permission denial handling",
            "Resource limitation testing",
            "User interaction errors",
            "Data validation failures",
            "System state conflicts",
            "Recovery mechanism testing"
        ]

        scenario_type = basic_scenarios[scenario_index % len(basic_scenarios)]

        return f"""
  Scenario: {scenario_type} in {feature_name}
    Given I launch the "Rumah Pendidikan" application
    When I navigate to the "{feature_name}" section
    And I trigger {scenario_type.lower()} conditions
    Then the application should handle the error gracefully
    And appropriate error messages should be displayed
    And the application should not crash
    And user should be able to recover from the error
"""

    def _generate_extra_negative_scenario(self, feature_name: str, scenario_index: int) -> str:
        """Generate extra negative scenarios when more are needed"""
        scenario_types = [
            "Invalid state transition",
            "Unexpected user behavior",
            "System resource conflict",
            "Data validation failure",
            "UI responsiveness issue",
            "Background process interference",
            "External service failure",
            "Cache corruption",
            "Configuration error",
            "Security violation"
        ]

        scenario_type = scenario_types[scenario_index % len(scenario_types)]

        scenario = f"""
  Scenario: {scenario_type} in {feature_name}
    Given I am on the {feature_name} screen
    When I launch the mobile application
    Then I should see the {feature_name} interface
    When I trigger {scenario_type.lower()} conditions
    Then the application should handle the error gracefully
    And appropriate error messages should be displayed
    And the application should not crash
    And user data should remain intact
"""
        return scenario

    def _generate_device_limitation_scenario(self, feature_name: str) -> str:
        """Generate device limitation testing scenario"""
        scenario = f"""
  Scenario: Device limitation testing in {feature_name}
    Given I am on the {feature_name} screen
    When I launch the mobile application
    Then I should see the {feature_name} interface
    When I simulate low battery conditions
    Then the application should handle low battery gracefully
    When I simulate low storage space
    Then the application should handle storage limitations
    When I simulate poor network connectivity
    Then the application should handle network issues appropriately
    And user experience should remain acceptable
"""
        return scenario

    def _generate_data_corruption_scenario(self, feature_name: str) -> str:
        """Generate data corruption testing scenario"""
        scenario = f"""
  Scenario: Data corruption handling in {feature_name}
    Given I am on the {feature_name} screen
    When I launch the mobile application
    Then I should see the {feature_name} interface
    When I simulate corrupted local data
    Then the application should detect data corruption
    When I attempt to access corrupted data
    Then appropriate error handling should occur
    And data recovery mechanisms should activate
    And user should be notified appropriately
"""
        return scenario

    def _generate_timeout_scenario(self, feature_name: str) -> str:
        """Generate timeout testing scenario"""
        scenario = f"""
  Scenario: Timeout handling in {feature_name}
    Given I am on the {feature_name} screen
    When I launch the mobile application
    Then I should see the {feature_name} interface
    When I trigger operations that may timeout
    Then the application should handle timeouts gracefully
    When I wait for extended periods without interaction
    Then session timeout should be handled appropriately
    And user should be notified of timeout conditions
    And recovery options should be provided
"""
        return scenario

    def _generate_resource_exhaustion_scenario(self, feature_name: str) -> str:
        """Generate resource exhaustion testing scenario"""
        scenario = f"""
  Scenario: Resource exhaustion handling in {feature_name}
    Given I am on the {feature_name} screen
    When I launch the mobile application
    Then I should see the {feature_name} interface
    When I consume excessive system resources
    Then the application should handle resource exhaustion
    When I trigger memory-intensive operations
    Then memory management should prevent crashes
    And system stability should be maintained
    And appropriate warnings should be displayed
"""
        return scenario

    def _generate_malformed_data_scenario(self, feature_name: str) -> str:
        """Generate malformed data testing scenario"""
        scenario = f"""
  Scenario: Malformed data handling in {feature_name}
    Given I am on the {feature_name} screen
    When I launch the mobile application
    Then I should see the {feature_name} interface
    When I provide malformed input data
    Then the application should validate input properly
    When I submit invalid data formats
    Then appropriate validation errors should be shown
    And data integrity should be maintained
    And security should not be compromised
"""
        return scenario

    def _generate_session_expiry_scenario(self, feature_name: str) -> str:
        """Generate session expiry testing scenario"""
        scenario = f"""
  Scenario: Session expiry handling in {feature_name}
    Given I am on the {feature_name} screen
    When I launch the mobile application
    Then I should see the {feature_name} interface
    When I remain inactive for extended periods
    Then session expiry should be handled gracefully
    When I attempt actions after session expiry
    Then appropriate authentication prompts should appear
    And user data should be preserved where possible
    And security should be maintained
"""
        return scenario

    async def _generate_security_scenarios(
        self, feature_name: str, feature_data: pd.DataFrame,
        clickable_elements: pd.DataFrame, input_elements: pd.DataFrame
    ) -> List[str]:
        """Generate security test scenarios"""
        security_scenarios = []

        try:
            # 1. Input Injection Security Scenario
            if not input_elements.empty:
                injection_scenario = self._generate_injection_security_scenario(
                    feature_name, input_elements
                )
                security_scenarios.append(injection_scenario)

            # 2. Authentication Security Scenario
            auth_security_scenario = self._generate_authentication_security_scenario(
                feature_name
            )
            security_scenarios.append(auth_security_scenario)

            # 3. Session Security Scenario
            session_security_scenario = self._generate_session_security_scenario(
                feature_name
            )
            security_scenarios.append(session_security_scenario)

            # 4. Data Privacy Scenario (if input elements exist)
            if not input_elements.empty:
                privacy_scenario = self._generate_data_privacy_scenario(
                    feature_name, input_elements
                )
                security_scenarios.append(privacy_scenario)

        except Exception as e:
            logger.error(f"Failed to generate security scenarios: {e}")

        return security_scenarios

    async def _generate_edge_case_scenarios(
        self, feature_name: str, feature_data: pd.DataFrame,
        clickable_elements: pd.DataFrame, input_elements: pd.DataFrame
    ) -> List[str]:
        """Generate edge case test scenarios"""
        edge_case_scenarios = []

        try:
            # 1. Performance Edge Case Scenario
            performance_scenario = self._generate_performance_edge_case_scenario(
                feature_name
            )
            edge_case_scenarios.append(performance_scenario)

            # 2. Memory Constraint Scenario
            memory_scenario = self._generate_memory_constraint_scenario(
                feature_name
            )
            edge_case_scenarios.append(memory_scenario)

            # 3. Concurrent Access Scenario
            if not clickable_elements.empty:
                concurrent_scenario = self._generate_concurrent_access_scenario(
                    feature_name, clickable_elements
                )
                edge_case_scenarios.append(concurrent_scenario)

        except Exception as e:
            logger.error(f"Failed to generate edge case scenarios: {e}")

        return edge_case_scenarios

    async def _generate_comprehensive_positive_scenarios(
        self, feature_name: str, feature_data: pd.DataFrame,
        clickable_elements: pd.DataFrame, input_elements: pd.DataFrame,
        scrollable_elements: pd.DataFrame, enhanced_context: Dict[str, Any]
    ) -> List[str]:
        """Generate comprehensive positive test scenarios"""
        positive_scenarios = []

        try:
            # 1. Professional Navigation Scenario
            if not clickable_elements.empty:
                prof_nav_scenario = self._generate_professional_navigation_scenario(
                    feature_name, clickable_elements
                )
                if prof_nav_scenario:
                    positive_scenarios.append(prof_nav_scenario)

            # 2. RAG-Enhanced Navigation Scenario
            if not clickable_elements.empty:
                rag_nav_scenario = await self._generate_rag_enhanced_navigation_scenario(
                    feature_name, clickable_elements, enhanced_context
                )
                if rag_nav_scenario:
                    positive_scenarios.append(rag_nav_scenario)

            # 3. Multiple Interaction Patterns Scenario
            if not clickable_elements.empty:
                interaction_scenario = self._generate_interaction_patterns_scenario(
                    feature_name, clickable_elements
                )
                positive_scenarios.append(interaction_scenario)

            # 4. Professional Input Scenario
            if not input_elements.empty:
                prof_input_scenario = self._generate_professional_input_scenario(
                    feature_name, input_elements
                )
                if prof_input_scenario:
                    positive_scenarios.append(prof_input_scenario)

            # 5. Data Validation Scenario
            if not input_elements.empty:
                data_validation_scenario = self._generate_data_validation_scenario(
                    feature_name, input_elements
                )
                positive_scenarios.append(data_validation_scenario)

            # 6. Scroll and Navigation Scenario
            if not scrollable_elements.empty:
                scroll_scenario = self._generate_enhanced_scroll_scenario(
                    feature_name, scrollable_elements
                )
                positive_scenarios.append(scroll_scenario)

            # 7. UI Validation Scenario
            validation_scenario = self._generate_validation_scenario(
                feature_name, feature_data
            )
            positive_scenarios.append(validation_scenario)

            # 8. User Journey Scenario
            user_journey_scenario = self._generate_user_journey_scenario(
                feature_name, clickable_elements, input_elements
            )
            positive_scenarios.append(user_journey_scenario)

        except Exception as e:
            logger.error(f"Failed to generate positive scenarios: {e}")

        return positive_scenarios

    async def _generate_comprehensive_negative_scenarios(
        self, feature_name: str, feature_data: pd.DataFrame,
        clickable_elements: pd.DataFrame, input_elements: pd.DataFrame
    ) -> List[str]:
        """Generate comprehensive negative test scenarios"""
        negative_scenarios = []

        try:
            # 1. Invalid Input Scenario
            if not input_elements.empty:
                invalid_input_scenario = self._generate_invalid_input_scenario(
                    feature_name, input_elements
                )
                negative_scenarios.append(invalid_input_scenario)

            # 2. Enhanced Error Handling Scenario
            if not clickable_elements.empty:
                error_handling_scenario = self._generate_enhanced_error_handling_scenario(
                    feature_name, clickable_elements
                )
                negative_scenarios.append(error_handling_scenario)

            # 3. Boundary Condition Scenario
            boundary_scenario = self._generate_boundary_scenario(
                feature_name, feature_data
            )
            negative_scenarios.append(boundary_scenario)

            # 4. Stress Testing Scenario
            if not clickable_elements.empty:
                stress_scenario = self._generate_stress_testing_scenario(
                    feature_name, clickable_elements
                )
                negative_scenarios.append(stress_scenario)

            # 5. Network Error Simulation Scenario
            network_error_scenario = self._generate_network_error_scenario(
                feature_name
            )
            negative_scenarios.append(network_error_scenario)

            # 6. Permission Denied Scenario
            permission_scenario = self._generate_permission_denied_scenario(
                feature_name
            )
            negative_scenarios.append(permission_scenario)

        except Exception as e:
            logger.error(f"Failed to generate negative scenarios: {e}")

        return negative_scenarios

    async def _generate_security_scenarios(
        self, feature_name: str, feature_data: pd.DataFrame,
        clickable_elements: pd.DataFrame, input_elements: pd.DataFrame
    ) -> List[str]:
        """Generate security test scenarios"""
        security_scenarios = []

        try:
            # 1. Input Injection Security Scenario
            if not input_elements.empty:
                injection_scenario = self._generate_injection_security_scenario(
                    feature_name, input_elements
                )
                security_scenarios.append(injection_scenario)

            # 2. Authentication Security Scenario
            auth_security_scenario = self._generate_authentication_security_scenario(
                feature_name
            )
            security_scenarios.append(auth_security_scenario)

            # 3. Session Security Scenario
            session_security_scenario = self._generate_session_security_scenario(
                feature_name
            )
            security_scenarios.append(session_security_scenario)

            # 4. Data Privacy Scenario (if input elements exist)
            if not input_elements.empty:
                privacy_scenario = self._generate_data_privacy_scenario(
                    feature_name, input_elements
                )
                security_scenarios.append(privacy_scenario)

        except Exception as e:
            logger.error(f"Failed to generate security scenarios: {e}")

        return security_scenarios

    async def _generate_edge_case_scenarios(
        self, feature_name: str, feature_data: pd.DataFrame,
        clickable_elements: pd.DataFrame, input_elements: pd.DataFrame
    ) -> List[str]:
        """Generate edge case test scenarios"""
        edge_case_scenarios = []

        try:
            # 1. Performance Edge Case Scenario
            performance_scenario = self._generate_performance_edge_case_scenario(
                feature_name
            )
            edge_case_scenarios.append(performance_scenario)

            # 2. Memory Constraint Scenario
            memory_scenario = self._generate_memory_constraint_scenario(
                feature_name
            )
            edge_case_scenarios.append(memory_scenario)

            # 3. Concurrent Access Scenario
            if not clickable_elements.empty:
                concurrent_scenario = self._generate_concurrent_access_scenario(
                    feature_name, clickable_elements
                )
                edge_case_scenarios.append(concurrent_scenario)

        except Exception as e:
            logger.error(f"Failed to generate edge case scenarios: {e}")

        return edge_case_scenarios

    def _generate_interaction_patterns_scenario(
        self, feature_name: str, clickable_elements: pd.DataFrame
    ) -> str:
        """Generate scenario testing multiple interaction patterns"""
        scenario = f"""
  Scenario: Multiple interaction patterns in {feature_name}
    Given I am on the {feature_name} screen
    When I launch the mobile application
    Then I should see the {feature_name} interface
"""

        # Get meaningful elements for different interaction types
        meaningful_elements = []
        for _, element in clickable_elements.iterrows():
            element_desc = self._get_element_description(element)
            if (element_desc and element_desc != "the UI element" and
                "unknown" not in element_desc.lower()):
                meaningful_elements.append((element, element_desc))
                if len(meaningful_elements) >= 3:
                    break

        for element, element_desc in meaningful_elements:
            scenario += f"    When I tap on {element_desc}\n"
            scenario += "    Then the application should respond to the tap\n"
            scenario += f"    When I long press on {element_desc}\n"
            scenario += "    Then the application should handle the long press\n"

        return scenario

    def _generate_data_validation_scenario(
        self, feature_name: str, input_elements: pd.DataFrame
    ) -> str:
        """Generate comprehensive data validation scenario"""
        scenario = f"""
  Scenario: Data validation in {feature_name}
    Given I am on the {feature_name} screen
    When I launch the mobile application
    Then I should see input fields
"""

        # Test different data types
        data_types = [
            ("valid text data", "Hello World"),
            ("numeric data", "12345"),
            ("special characters", "Test@123"),
            ("empty data", "")
        ]

        limited_elements = input_elements.head(2)
        for _, element in limited_elements.iterrows():
            element_desc = self._get_element_description(element)
            if element_desc and element_desc != "the UI element":
                for data_type, test_data in data_types:
                    scenario += f"    When I enter \"{test_data}\" as {data_type} in {element_desc}\n"
                    scenario += f"    Then the field should accept or validate the {data_type}\n"

        return scenario

    def _generate_enhanced_scroll_scenario(
        self, feature_name: str, scrollable_elements: pd.DataFrame
    ) -> str:
        """Generate enhanced scroll testing scenario"""
        scenario = f"""
  Scenario: Enhanced scroll functionality in {feature_name}
    Given I am on the {feature_name} screen
    When I launch the mobile application
    Then I should see scrollable content
    When I scroll down slowly
    Then the content should scroll smoothly
    When I scroll up quickly
    Then the content should scroll back smoothly
    When I perform fling gesture downward
    Then the content should scroll with momentum
    When I perform fling gesture upward
    Then the content should scroll back with momentum
    And the scroll position should be maintained correctly
"""
        return scenario

    def _generate_user_journey_scenario(
        self, feature_name: str, clickable_elements: pd.DataFrame, input_elements: pd.DataFrame
    ) -> str:
        """Generate end-to-end user journey scenario"""
        scenario = f"""
  Scenario: Complete user journey in {feature_name}
    Given I am on the {feature_name} screen
    When I launch the mobile application
    Then I should see the {feature_name} interface
"""

        # Add navigation steps
        if not clickable_elements.empty:
            element = clickable_elements.iloc[0]
            element_desc = self._get_element_description(element)
            if element_desc != "the UI element":
                scenario += f"    When I navigate through {element_desc}\n"
                scenario += "    Then I should see the navigation response\n"

        # Add input steps if available
        if not input_elements.empty:
            element = input_elements.iloc[0]
            element_desc = self._get_element_description(element)
            if element_desc != "the UI element":
                scenario += f"    When I enter user data in {element_desc}\n"
                scenario += "    Then the data should be accepted\n"

        scenario += "    When I complete the user journey\n"
        scenario += "    Then all interactions should work as expected\n"
        scenario += "    And the user experience should be smooth\n"

        return scenario

    def _generate_enhanced_error_handling_scenario(
        self, feature_name: str, clickable_elements: pd.DataFrame
    ) -> str:
        """Generate enhanced error handling scenario"""
        scenario = f"""
  Scenario: Enhanced error handling in {feature_name}
    Given I am on the {feature_name} screen
    When I launch the mobile application
    Then I should see the {feature_name} interface
"""

        meaningful_elements = []
        for _, element in clickable_elements.iterrows():
            element_desc = self._get_element_description(element)
            if (element_desc and element_desc != "the UI element" and
                "unknown" not in element_desc.lower()):
                meaningful_elements.append((element, element_desc))
                if len(meaningful_elements) >= 2:
                    break

        for element, element_desc in meaningful_elements:
            scenario += f"    When I rapidly tap {element_desc} multiple times\n"
            scenario += "    Then the application should not crash or freeze\n"
            scenario += f"    When I tap {element_desc} while network is unavailable\n"
            scenario += "    Then the application should handle network errors gracefully\n"

        return scenario

    def _generate_stress_testing_scenario(
        self, feature_name: str, clickable_elements: pd.DataFrame
    ) -> str:
        """Generate stress testing scenario"""
        scenario = f"""
  Scenario: Stress testing in {feature_name}
    Given I am on the {feature_name} screen
    When I launch the mobile application
    Then I should see the {feature_name} interface
    When I perform rapid consecutive interactions
    Then the application should remain stable
    When I perform simultaneous multi-touch gestures
    Then the application should handle multi-touch correctly
    When I rotate the device multiple times quickly
    Then the application should handle orientation changes
    And the interface should remain responsive
    And no memory leaks should occur
"""
        return scenario

    def _generate_network_error_scenario(self, feature_name: str) -> str:
        """Generate network error simulation scenario"""
        scenario = f"""
  Scenario: Network error handling in {feature_name}
    Given I am on the {feature_name} screen
    When I launch the mobile application with no network connection
    Then I should see appropriate offline message
    When I enable network connection
    Then the application should reconnect automatically
    When I simulate slow network connection
    Then the application should handle slow responses gracefully
    When I simulate network timeout
    Then the application should show timeout error message
    And provide retry options to the user
"""
        return scenario

    def _generate_permission_denied_scenario(self, feature_name: str) -> str:
        """Generate permission denied scenario"""
        scenario = f"""
  Scenario: Permission denied handling in {feature_name}
    Given I am on the {feature_name} screen
    When I launch the mobile application
    Then I should see the {feature_name} interface
    When I deny required permissions
    Then the application should show permission request explanation
    When I grant permissions after denial
    Then the application should work normally
    When I permanently deny permissions
    Then the application should provide alternative functionality
    And guide user to settings if needed
"""
        return scenario

    # ========== SECURITY SCENARIO GENERATION METHODS ==========

    def _generate_injection_security_scenario(
        self, feature_name: str, input_elements: pd.DataFrame
    ) -> str:
        """Generate input injection security testing scenario"""
        scenario = f"""
  Scenario: Input injection security testing in {feature_name}
    Given I am on the {feature_name} screen
    When I launch the mobile application
    Then I should see input fields
"""

        # Security test payloads
        security_payloads = [
            ("SQL injection", "'; DROP TABLE users; --"),
            ("XSS injection", "<script>alert('XSS')</script>"),
            ("Command injection", "; rm -rf /"),
            ("Path traversal", "../../../etc/passwd"),
            ("LDAP injection", "admin)(&(password=*))")
        ]

        limited_elements = input_elements.head(2)
        for _, element in limited_elements.iterrows():
            element_desc = self._get_element_description(element)
            if element_desc and element_desc != "the UI element":
                for attack_type, payload in security_payloads:
                    scenario += f"    When I enter {attack_type} payload \"{payload}\" in {element_desc}\n"
                    scenario += f"    Then the application should sanitize the {attack_type} input\n"
                    scenario += "    And no security vulnerability should be exploited\n"

        return scenario

    def _generate_authentication_security_scenario(self, feature_name: str) -> str:
        """Generate authentication security testing scenario"""
        scenario = f"""
  Scenario: Authentication security testing in {feature_name}
    Given I am on the {feature_name} screen
    When I launch the mobile application
    Then I should see the {feature_name} interface
    When I attempt to access without authentication
    Then I should be prompted for authentication
    When I provide invalid authentication credentials
    Then the application should reject the access
    When I attempt brute force authentication
    Then the application should implement rate limiting
    When I provide valid authentication credentials
    Then I should gain appropriate access
    And the session should be securely managed
"""
        return scenario

    def _generate_session_security_scenario(self, feature_name: str) -> str:
        """Generate session security testing scenario"""
        scenario = f"""
  Scenario: Session security testing in {feature_name}
    Given I am on the {feature_name} screen
    When I launch the mobile application
    Then I should see the {feature_name} interface
    When I establish a user session
    Then the session should be securely created
    When I remain idle for extended period
    Then the session should timeout appropriately
    When I attempt session hijacking
    Then the application should detect and prevent it
    When I logout from the application
    Then the session should be completely terminated
    And no session data should remain accessible
"""
        return scenario

    def _generate_data_privacy_scenario(
        self, feature_name: str, input_elements: pd.DataFrame
    ) -> str:
        """Generate data privacy testing scenario"""
        scenario = f"""
  Scenario: Data privacy testing in {feature_name}
    Given I am on the {feature_name} screen
    When I launch the mobile application
    Then I should see the {feature_name} interface
"""

        limited_elements = input_elements.head(2)
        for _, element in limited_elements.iterrows():
            element_desc = self._get_element_description(element)
            if element_desc and element_desc != "the UI element":
                scenario += f"    When I enter sensitive data in {element_desc}\n"
                scenario += "    Then the data should be encrypted in transit\n"
                scenario += "    And the data should be encrypted at rest\n"
                scenario += "    When I view the data in logs\n"
                scenario += "    Then sensitive data should be masked or redacted\n"

        scenario += "    When I request data deletion\n"
        scenario += "    Then all personal data should be completely removed\n"
        scenario += "    And data retention policies should be enforced\n"

        return scenario

    # ========== EDGE CASE SCENARIO GENERATION METHODS ==========

    def _generate_performance_edge_case_scenario(self, feature_name: str) -> str:
        """Generate performance edge case testing scenario"""
        scenario = f"""
  Scenario: Performance edge case testing in {feature_name}
    Given I am on the {feature_name} screen
    When I launch the mobile application
    Then I should see the {feature_name} interface
    When I load large amounts of data
    Then the application should handle large datasets efficiently
    When I perform operations under low memory conditions
    Then the application should manage memory gracefully
    When I test with slow device performance
    Then the application should remain responsive
    When I test with high CPU usage
    Then the application should not become unresponsive
    And performance should degrade gracefully
"""
        return scenario

    def _generate_memory_constraint_scenario(self, feature_name: str) -> str:
        """Generate memory constraint testing scenario"""
        scenario = f"""
  Scenario: Memory constraint testing in {feature_name}
    Given I am on the {feature_name} screen
    When I launch the mobile application under low memory conditions
    Then the application should start successfully
    When I perform memory-intensive operations
    Then the application should manage memory efficiently
    When I simulate out-of-memory conditions
    Then the application should handle memory errors gracefully
    When I monitor memory usage over time
    Then there should be no memory leaks
    And memory usage should remain within acceptable limits
"""
        return scenario

    def _generate_concurrent_access_scenario(
        self, feature_name: str, clickable_elements: pd.DataFrame
    ) -> str:
        """Generate concurrent access testing scenario"""
        scenario = f"""
  Scenario: Concurrent access testing in {feature_name}
    Given I am on the {feature_name} screen
    When I launch the mobile application
    Then I should see the {feature_name} interface
    When I perform simultaneous operations
    Then the application should handle concurrent access
    When I access the same resource from multiple points
    Then data consistency should be maintained
    When I perform conflicting operations simultaneously
    Then the application should resolve conflicts appropriately
    And data integrity should be preserved
"""
        return scenario

    def _extract_navigation_patterns_from_rag(self, enhanced_context: Dict[str, Any]) -> List[str]:
        """Extract navigation patterns from RAG context"""
        patterns = []
        try:
            for query, results in enhanced_context.items():
                if 'navigation' in query.lower() or 'flow' in query.lower():
                    for result in results:
                        content = result.get('content', '')
                        # Extract common navigation patterns
                        if 'tap' in content.lower():
                            patterns.append('tap_interaction')
                        if 'swipe' in content.lower():
                            patterns.append('swipe_gesture')
                        if 'scroll' in content.lower():
                            patterns.append('scroll_action')
                        if 'navigate' in content.lower():
                            patterns.append('screen_navigation')
        except Exception as e:
            logger.warning(f"Failed to extract navigation patterns: {e}")

        return list(set(patterns))  # Remove duplicates

    def _get_meaningful_elements_with_rag(self, elements: pd.DataFrame, enhanced_context: Dict[str, Any]) -> List[tuple]:
        """Get meaningful elements enhanced with RAG context"""
        meaningful_elements = []

        try:
            for _, element in elements.iterrows():
                element_desc = self._get_element_description(element)

                # Skip generic descriptions
                if (element_desc and element_desc != "the UI element" and
                    "unknown" not in element_desc.lower() and len(element_desc) > 10):

                    # Enhance with RAG context
                    context_info = self._get_element_context_from_rag(element, enhanced_context)
                    meaningful_elements.append((element, element_desc, context_info))

                    if len(meaningful_elements) >= 5:  # Limit for quality
                        break

        except Exception as e:
            logger.warning(f"Failed to get meaningful elements with RAG: {e}")

        return meaningful_elements

    def _create_contextual_scenario_title(self, feature_name: str, scenario_type: str, patterns: List[str]) -> str:
        """Create contextual scenario title based on RAG patterns"""
        try:
            if patterns:
                if 'screen_navigation' in patterns:
                    return f"Navigate through {feature_name} screens effectively"
                elif 'tap_interaction' in patterns:
                    return f"Interact with {feature_name} interface elements"
                elif 'scroll_action' in patterns:
                    return f"Explore {feature_name} content through scrolling"
                else:
                    return f"Test {feature_name} {scenario_type} functionality"
            else:
                return f"Verify {feature_name} {scenario_type} behavior"
        except Exception:
            return f"Test {feature_name} {scenario_type}"

    def _get_element_context_from_rag(self, element: pd.Series, enhanced_context: Dict[str, Any]) -> Dict[str, str]:
        """Get element context information from RAG"""
        context_info = {'purpose': 'interact with the interface', 'expected_action': None}

        try:
            element_text = str(element.get('text', '')).lower()
            element_desc = str(element.get('content_desc', '')).lower()
            element_id = str(element.get('resource_id', '')).lower()

            # Analyze element based on RAG knowledge
            for query, results in enhanced_context.items():
                for result in results:
                    content = result.get('content', '').lower()

                    # Match element characteristics with RAG knowledge
                    if any(keyword in element_text or keyword in element_desc or keyword in element_id
                           for keyword in ['button', 'btn']):
                        if 'submit' in content or 'save' in content:
                            context_info['purpose'] = 'submit or save data'
                            context_info['expected_action'] = 'I should see a confirmation or success message'
                        elif 'cancel' in content or 'back' in content:
                            context_info['purpose'] = 'cancel the current action'
                            context_info['expected_action'] = 'I should return to the previous screen'
                        else:
                            context_info['purpose'] = 'trigger an action'
                            context_info['expected_action'] = 'I should see the interface respond'

                    elif any(keyword in element_text or keyword in element_desc
                            for keyword in ['menu', 'navigation']):
                        context_info['purpose'] = 'access navigation options'
                        context_info['expected_action'] = 'I should see navigation menu or options'

                    elif any(keyword in element_text or keyword in element_desc
                            for keyword in ['search', 'find']):
                        context_info['purpose'] = 'search for content'
                        context_info['expected_action'] = 'I should see search results or input field'

        except Exception as e:
            logger.warning(f"Failed to get element context from RAG: {e}")

        return context_info

    def _get_element_description(self, element: pd.Series) -> str:
        """Get human-readable description of an element"""
        def clean_text(text):
            """Clean text by removing newlines, extra spaces, and special characters"""
            if not text:
                return ""
            # Remove newlines and extra whitespace
            cleaned = re.sub(r'\s+', ' ', str(text).strip())
            # Remove special characters that might break Gherkin
            cleaned = re.sub(r'["\n\r\t]', '', cleaned)
            return cleaned

        # Try text first
        if pd.notna(element.get('text')) and element.get('text'):
            text = clean_text(element['text'])
            if text and len(text) <= 50:  # Reasonable length limit
                return f"the \"{text}\" element"

        # Try content_desc
        if (pd.notna(element.get('content_desc')) and element.get('content_desc')):
            content_desc = clean_text(element['content_desc'])
            if content_desc and len(content_desc) <= 50:
                return f"the \"{content_desc}\" element"

        # Try resource_id
        if (pd.notna(element.get('resource_id')) and element.get('resource_id')):
            resource_id = (
                str(element['resource_id']).split('/')[-1]
                if '/' in str(element['resource_id'])
                else str(element['resource_id'])
            )
            resource_id = clean_text(resource_id)
            if resource_id and len(resource_id) <= 30:
                return f"the {resource_id} element"

        # Fallback to class name, but avoid "unknown"
        if 'class_name' in element:
            class_name_value = element.get('class_name', '')
            if pd.notna(class_name_value) and isinstance(class_name_value, str) and class_name_value:
                class_name = class_name_value.split('.')[-1]
                if class_name and class_name.lower() not in ['view', 'viewgroup', 'linearlayout', 'relativelayout']:
                    return f"the {class_name} element"
        
        # Try element_type if class_name is not available
        if 'element_type' in element:
            element_type = element.get('element_type', '')
            if pd.notna(element_type) and isinstance(element_type, str) and element_type:
                if element_type.lower() not in ['view', 'viewgroup', 'layout']:
                    return f"the {element_type} element"
                    
        # Try action as last resort
        if 'action' in element:
            action = element.get('action', '')
            if pd.notna(action) and isinstance(action, str) and action:
                action_clean = re.sub(r'[^a-zA-Z0-9 ]', '', action).strip()
                if action_clean and len(action_clean) <= 30:
                    return f"the element for {action_clean}"

        # Return a generic description for elements that can't be meaningfully described
        return "the UI element"

    def _is_scenario_duplicate(self, scenario_content: str) -> bool:
        """Check if scenario is duplicate or too similar to existing ones"""
        # Create a simplified hash of the scenario
        import hashlib

        # Normalize scenario content for comparison
        normalized = re.sub(r'\s+', ' ', scenario_content.lower().strip())
        normalized = re.sub(r'scenario:.*?\n', '', normalized)  # Remove scenario title

        scenario_hash = hashlib.md5(normalized.encode()).hexdigest()

        if scenario_hash in self.generated_scenarios:
            return True

        self.generated_scenarios.add(scenario_hash)
        return False

    def _generate_mobile_ui_step(self, action: str, target: str = "", value: str = "", context: str = "") -> str:
        """Generate mobile UI-focused step that makes sense for emulator execution"""

        # Mobile UI action patterns that users can visually follow
        mobile_patterns = {
            "launch": f'Given I launch the "Rumah Pendidikan" application',
            "tap": f'When I tap on "{target}" button' if target else 'When I tap on the element',
            "enter": f'When I enter "{value}" in the "{target}" field' if target and value else f'When I enter text in the field',
            "see": f'Then I should see "{target}"' if target else 'Then I should see the element',
            "navigate": f'When I navigate to "{target}" section' if target else 'When I navigate to the section',
            "scroll": f'When I scroll {target} on the screen' if target else 'When I scroll on the screen',
            "verify": f'Then I should see "{target}" displayed correctly' if target else 'Then I should see the content',
            "wait": f'And I wait for "{target}" to load' if target else 'And I wait for the content to load',
            "back": 'When I tap the back button',
            "menu": f'When I tap on "{target}" menu item' if target else 'When I tap on the menu item',
            "select": f'When I select "{value}" from "{target}" dropdown' if target and value else 'When I make a selection',
            "swipe": f'When I swipe {target} on the screen' if target else 'When I swipe on the screen'
        }

        return mobile_patterns.get(action, f'When I perform {action} action')

    def _create_mobile_ui_scenario(self, scenario_title: str, feature_name: str, steps: List[Dict[str, str]]) -> str:
        """Create a complete mobile UI-focused scenario that makes sense for emulator execution"""

        scenario = f"""
  Scenario: {scenario_title}
    Given I launch the "Rumah Pendidikan" application
    When I navigate to the "{feature_name}" section
    Then I should see the "{feature_name}" interface loaded
"""

        # Add mobile UI-focused steps
        for step in steps:
            action = step.get('action', '')
            target = step.get('target', '')
            value = step.get('value', '')

            if action and target:
                mobile_step = self._generate_mobile_ui_step(action, target, value)
                scenario += f"    {mobile_step}\n"

                # Add appropriate verification step
                if action in ['tap', 'enter', 'select']:
                    scenario += f"    Then the application should respond appropriately\n"
                elif action == 'navigate':
                    scenario += f"    Then I should see the \"{target}\" content\n"

        # Add final verification
        scenario += "    And all interactions should work as expected\n"
        scenario += "    And the user experience should be smooth\n"

        return scenario

    def _validate_mobile_ui_scenario(self, scenario: str) -> bool:
        """Validate that scenario contains mobile UI-focused steps that make sense for emulator execution"""

        # Keywords that indicate abstract/backend operations (should be avoided)
        abstract_keywords = [
            'database', 'api', 'backend', 'service', 'server', 'connection',
            'integration', 'synchronization', 'transaction', 'rollback',
            'foreign key', 'data integrity', 'cache', 'storage quota'
        ]

        # Keywords that indicate mobile UI operations (should be present)
        mobile_ui_keywords = [
            'launch', 'tap', 'click', 'enter', 'see', 'navigate', 'scroll',
            'button', 'field', 'screen', 'interface', 'menu', 'select',
            'swipe', 'application', 'should see', 'should be'
        ]

        scenario_lower = scenario.lower()

        # Check for abstract keywords (red flags)
        abstract_count = sum(1 for keyword in abstract_keywords if keyword in scenario_lower)

        # Check for mobile UI keywords (good signs)
        mobile_count = sum(1 for keyword in mobile_ui_keywords if keyword in scenario_lower)

        # Scenario is valid if it has more mobile UI keywords than abstract ones
        # and has at least 3 mobile UI keywords
        return mobile_count >= 3 and mobile_count > abstract_count

    def _improve_scenario_for_mobile_ui(self, scenario: str, feature_name: str) -> str:
        """Improve scenario to be more mobile UI-focused and user-friendly"""

        lines = scenario.split('\n')
        improved_lines = []

        for line in lines:
            line_lower = line.lower().strip()

            # Skip empty lines and preserve structure
            if not line_lower or line_lower.startswith('scenario:') or line_lower.startswith('feature:'):
                improved_lines.append(line)
                continue

            # Improve abstract steps to be mobile UI-focused
            if 'database' in line_lower and 'create' in line_lower:
                improved_lines.append('    When I tap on "Create Profile" or "Add New" button')
                improved_lines.append('    And I fill in the required profile information')
                improved_lines.append('    And I tap "Save" or "Submit" button')
                improved_lines.append('    Then I should see "Profile created successfully" message')

            elif 'database' in line_lower and 'read' in line_lower:
                improved_lines.append('    When I navigate to "My Profile" or "Account" section')
                improved_lines.append('    Then I should see my profile information displayed')

            elif 'database' in line_lower and 'update' in line_lower:
                improved_lines.append('    When I tap "Edit Profile" or "Update" button')
                improved_lines.append('    And I modify the profile information')
                improved_lines.append('    And I tap "Save Changes" button')
                improved_lines.append('    Then I should see "Profile updated successfully" message')

            elif 'database' in line_lower and 'delete' in line_lower:
                improved_lines.append('    When I tap "Delete" or "Remove" button')
                improved_lines.append('    And I confirm the deletion action')
                improved_lines.append('    Then I should see "Item deleted successfully" message')

            elif 'api' in line_lower and 'authentication' in line_lower:
                improved_lines.append('    When I enter my credentials in the login form')
                improved_lines.append('    And I tap "Sign In" or "Login" button')
                improved_lines.append('    Then I should be logged in successfully')

            elif 'api' in line_lower and 'fetch' in line_lower:
                improved_lines.append('    When I navigate to the content section')
                improved_lines.append('    Then I should see the content loaded from server')

            elif 'service' in line_lower and 'unavailable' in line_lower:
                improved_lines.append('    When I try to access online features while offline')
                improved_lines.append('    Then I should see "Connection error" or "Offline mode" message')

            else:
                # Keep the original line if it's already mobile UI-focused
                improved_lines.append(line)

        return '\n'.join(improved_lines)

    def _generate_executable_step(self, step_type: str, **kwargs) -> str:
        """Generate executable step using supported patterns"""
        if step_type not in self.supported_steps:
            return ""

        patterns = self.supported_steps[step_type]["patterns"]

        # Choose appropriate pattern based on available data
        if step_type == "tap_element" and "element" in kwargs:
            element = kwargs["element"]
            if element and element != "the UI element":
                return f'When I tap on "{element}"'

        elif step_type == "enter_text" and "text" in kwargs and "element" in kwargs:
            text = kwargs["text"]
            element = kwargs["element"]
            if element and element != "the UI element":
                return f'When I enter "{text}" in the "{element}" field'

        elif step_type == "verify_element" and "element" in kwargs:
            element = kwargs["element"]
            if element and element != "the UI element":
                return f'Then I should see {element}'

        elif step_type == "verify_screen" and "screen" in kwargs:
            screen = kwargs["screen"]
            return f'Then I should see the {screen} screen'

        # Return first pattern as default
        return patterns[0] if patterns else ""

    async def _save_feature_file(
        self, feature_name: str, scenarios: List[str]
    ) -> str:
        """Save feature file with scenarios"""
        try:
            # Create feature file content
            feature_content = f"""Feature: {feature_name}
  As a mobile app user
  I want to interact with the {feature_name} feature
  So that I can use the application effectively

"""

            # Add all scenarios
            for scenario in scenarios:
                feature_content += scenario + "\n"

            # Create filename
            filename = feature_name.lower().replace(" ", "_") + ".feature"

            # Get output path and create filepath
            config_path = self.config.get("GHERKIN_OUTPUT_PATH")
            filepath = os.path.join(config_path, filename)

            # Save file
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(feature_content)

            console.print(f"[green]Generated feature file: {filename}[/green]")

            return filepath

        except Exception as e:
            logger.error(
                f"Failed to save feature file for {feature_name}: {e}"
            )
            raise

    def _parse_custom_instructions(self, instructions: str) -> List[Dict[str, Any]]:
        """Enhanced parsing of custom instructions to extract detailed test case information"""
        try:
            parsed_instructions = []

            # Extract test case type (positive/negative) with enhanced detection
            case_type = self._detect_case_type_from_instructions(instructions)

            # Enhanced parsing for complex navigation instructions
            navigation_steps = self._extract_navigation_steps(instructions)
            assertions = self._extract_assertions(instructions)

            # Extract main feature/screen name with better patterns
            feature_name = self._extract_main_feature_name(instructions)

            # Create comprehensive instruction data
            instruction_data = {
                "case_type": case_type,
                "feature_name": feature_name,
                "navigation_steps": navigation_steps,
                "assertions": assertions,
                "original_instruction": instructions
            }

            parsed_instructions.append(instruction_data)
            return parsed_instructions

        except Exception as e:
            logger.error(f"Failed to parse custom instructions: {e}")
            return []

    def _extract_navigation_steps(self, instructions: str) -> List[Dict[str, str]]:
        """Extract navigation steps from complex instructions"""
        steps = []

        # Pattern for "click X and inside the page click Y"
        complex_nav_pattern = r'click\s+([^,]+?)\s+and\s+inside\s+(?:the\s+)?page\s+click\s+([^,]+?)(?:\s+and\s+then\s+(.+?))?(?:\s*$|\s+and\s+)'
        complex_match = re.search(complex_nav_pattern, instructions, re.IGNORECASE)

        if complex_match:
            first_element = complex_match.group(1).strip()
            second_element = complex_match.group(2).strip()

            steps.append({
                "action": "click",
                "target": first_element,
                "description": f"Click on {first_element}"
            })

            steps.append({
                "action": "click",
                "target": second_element,
                "description": f"Click on {second_element} inside the page"
            })
        else:
            # Fallback to simple click patterns
            click_patterns = [
                r'click\s+(?:on\s+)?([^,]+)',
                r'tap\s+(?:on\s+)?([^,]+)',
                r'press\s+(?:on\s+)?([^,]+)'
            ]

            for pattern in click_patterns:
                matches = re.finditer(pattern, instructions, re.IGNORECASE)
                for match in matches:
                    target = match.group(1).strip()
                    steps.append({
                        "action": "click",
                        "target": target,
                        "description": f"Click on {target}"
                    })

        return steps

    def _extract_assertions(self, instructions: str) -> List[Dict[str, str]]:
        """Extract assertion requirements from instructions"""
        assertions = []

        # Enhanced pattern for "assert text X" - capture everything after "assert text" until end or "and"
        text_assert_patterns = [
            r'assert\s+text\s+([^?]+\?)',  # Capture text ending with question mark
            r'assert\s+text\s+["\']([^"\']+)["\']',  # Quoted text
            r'assert\s+text\s+(.+?)(?:\s*$|\s+and\s+)',  # Text until end or "and"
        ]

        for pattern in text_assert_patterns:
            text_matches = re.finditer(pattern, instructions, re.IGNORECASE)
            for match in text_matches:
                text_content = match.group(1).strip()
                assertions.append({
                    "type": "text_assertion",
                    "content": text_content,
                    "description": f"Verify text '{text_content}' is displayed"
                })
                break  # Use first successful pattern

        # Pattern for "then assert X"
        then_assert_pattern = r'then\s+assert\s+(.+?)(?:\s*$|\s+and\s+)'
        then_matches = re.finditer(then_assert_pattern, instructions, re.IGNORECASE)

        for match in then_matches:
            assertion_content = match.group(1).strip()
            assertions.append({
                "type": "general_assertion",
                "content": assertion_content,
                "description": f"Verify {assertion_content}"
            })

        return assertions

    def _extract_main_feature_name(self, instructions: str) -> str:
        """Extract the main feature name from instructions with enhanced patterns"""
        # Try multiple patterns to extract feature name - prioritize the first navigation target
        patterns = [
            r'on\s+main\s+page\s+application\s+click\s+([^,\s]+(?:\s+[^,\s]+)*?)(?:\s+and\s+)',  # "on main page application click Ruang Murid and..."
            r'click\s+([^,\s]+(?:\s+[^,\s]+)*?)(?:\s+and\s+inside)',  # "click Ruang Murid and inside..."
            r'go\s+to\s+([^,\s]+(?:\s+[^,\s]+)*)',  # "go to Ruang Murid"
            r'navigate\s+to\s+([^,]+)',  # "navigate to X"
            r'in\s+(?:the\s+)?([^,]+)',  # "in the page"
            r'from\s+([^,]+)',  # "from main page"
        ]

        for pattern in patterns:
            match = re.search(pattern, instructions, re.IGNORECASE)
            if match:
                feature_name = match.group(1).strip()
                # Clean and format the feature name
                feature_name = re.sub(r'[^\w\s]', '', feature_name)
                feature_name = ' '.join(word.capitalize() for word in feature_name.split())

                # For navigation scenarios, combine with the second target for better naming
                if "and inside" in instructions.lower():
                    # Extract second navigation target for combined feature name
                    second_target_pattern = r'inside\s+(?:the\s+)?page\s+click\s+([^,\s]+(?:\s+[^,\s]+)*?)(?:\s+and\s+|$)'
                    second_match = re.search(second_target_pattern, instructions, re.IGNORECASE)
                    if second_match:
                        second_target = second_match.group(1).strip()
                        second_target = re.sub(r'[^\w\s]', '', second_target)
                        second_target = ' '.join(word.capitalize() for word in second_target.split())
                        return f"{feature_name} {second_target} Navigation"

                return feature_name

        # Default if no pattern matches
        return "Custom Test Feature"

    def _generate_custom_scenario(self, instruction: Dict[str, Any]) -> List[str]:
        """Generate enhanced Gherkin scenarios from parsed instruction with proper element locators"""
        try:
            case_type = instruction.get("case_type", "positive")
            feature_name = instruction.get("feature_name", "Custom Test Feature")
            navigation_steps = instruction.get("navigation_steps", [])
            assertions = instruction.get("assertions", [])

            # Generate multiple scenarios based on the complexity of instructions
            scenarios = []

            # Main navigation scenario
            main_scenario = self._generate_main_navigation_scenario(
                case_type, feature_name, navigation_steps, assertions
            )
            scenarios.append(main_scenario)

            # Element verification scenario (if we have navigation steps)
            if navigation_steps:
                verification_scenario = self._generate_element_verification_scenario(
                    feature_name, navigation_steps
                )
                scenarios.append(verification_scenario)

            # Negative scenario (error handling)
            if assertions:
                negative_scenario = self._generate_negative_scenario(
                    feature_name, navigation_steps, assertions
                )
                scenarios.append(negative_scenario)

            return scenarios

        except Exception as e:
            logger.error(f"Failed to generate custom scenario: {e}")
            return []

    def _generate_main_navigation_scenario(self, case_type: str, feature_name: str,
                                         navigation_steps: List[Dict], assertions: List[Dict]) -> str:
        """Generate the main navigation scenario with proper element locators"""
        scenario_title = f"Navigate to {feature_name} and access features with verification"

        scenario = f"""
  Scenario: {scenario_title}
    Given I am on the main page application
    When I launch the mobile application
    Then I should see the main page interface"""

        # Validate and add navigation steps with element locators from analysis
        valid_steps = []
        for step in navigation_steps:
            target = step.get("target", "element")

            # Check if element exists in analysis data
            if self._validate_element_exists_in_analysis(target):
                # Element exists, use proper accessibility ID
                accessibility_id = self._get_element_accessibility_id(target)
                valid_steps.append((target, accessibility_id, step))
                console.print(f"[green]✅ Element '{target}' found in analysis data[/green]")
            else:
                # Element doesn't exist, warn and provide guidance
                console.print(f"[red]❌ Element '{target}' not found in analysis data![/red]")
                console.print(f"[yellow]{self._get_available_elements_summary()}[/yellow]")
                # For now, we'll still create the step but mark it as potentially unreliable
                valid_steps.append((target, target, step))

        # Add navigation steps
        for target, accessibility_id, step in valid_steps:
            scenario += f"""
    When I click on "{target}" button using accessibility id "{accessibility_id}\""""

            # Add navigation verification
            if "inside" in step.get("description", "").lower():
                scenario += f"""
    Then I should be navigated to the {target} page
    And I should see the {target} interface"""

        # Add assertions
        for assertion in assertions:
            if assertion.get("type") == "text_assertion":
                text_content = assertion.get("content", "")
                scenario += f"""
    And I should see the text "{text_content}" displayed on the screen"""

        scenario += """
    And I should verify that the navigation is successful"""

        return scenario

    def _generate_element_verification_scenario(self, feature_name: str, navigation_steps: List[Dict]) -> str:
        """Generate element verification scenario"""
        scenario_title = f"Verify {feature_name} navigation elements"

        scenario = f"""
  Scenario: {scenario_title}
    Given I am on the main page application"""

        if navigation_steps:
            first_step = navigation_steps[0]
            target = first_step.get("target", "element")

            # Validate element exists before creating test step
            if self._validate_element_exists_in_analysis(target):
                accessibility_id = self._get_element_accessibility_id(target)
            else:
                console.print(f"[yellow]⚠️  Warning: Element '{target}' not found in analysis data for verification scenario.[/yellow]")
                accessibility_id = target

            scenario += f"""
    When I click on "{target}" button using accessibility id "{accessibility_id}"
    Then I should see the {target} page with available options"""

            # Add verification for subsequent elements
            for step in navigation_steps[1:]:
                step_target = step.get("target", "element")
                if self._validate_element_exists_in_analysis(step_target):
                    scenario += f"""
    And I should see "{step_target}" option is available
    And the {step_target} button should be clickable"""
                else:
                    console.print(f"[yellow]⚠️  Warning: Element '{step_target}' not found in analysis data for verification.[/yellow]")
                    scenario += f"""
    And I should see "{step_target}" option is available
    And the {step_target} button should be clickable"""

        return scenario

    def _generate_negative_scenario(self, feature_name: str, navigation_steps: List[Dict],
                                  assertions: List[Dict]) -> str:
        """Generate negative test scenario for error handling"""
        scenario_title = f"Negative case - Verify error handling when elements are not found"

        scenario = f"""
  Scenario: {scenario_title}
    Given I am on the main page application"""

        # Add navigation steps with validation
        for step in navigation_steps:
            target = step.get("target", "element")

            # Validate element exists before creating test step
            if self._validate_element_exists_in_analysis(target):
                accessibility_id = self._get_element_accessibility_id(target)
            else:
                console.print(f"[yellow]⚠️  Warning: Element '{target}' not found in analysis data for negative scenario.[/yellow]")
                accessibility_id = target

            scenario += f"""
    When I click on "{target}" button using accessibility id "{accessibility_id}\""""

        # Add negative assertions
        for assertion in assertions:
            if assertion.get("type") == "text_assertion":
                text_content = assertion.get("content", "")
                scenario += f"""
    Then if the text "{text_content}" is not displayed
    Then the application should handle the missing text gracefully"""

        scenario += f"""
    And I should still be able to navigate within the {feature_name} section"""

        return scenario

    def _get_element_accessibility_id(self, element_name: str) -> str:
        """Get accessibility ID for element from analysis data"""
        try:
            if hasattr(self, 'analysis_data') and self.analysis_data is not None:
                # Search for element in analysis data
                element_matches = self.analysis_data[
                    (self.analysis_data['text'].str.contains(element_name, case=False, na=False)) |
                    (self.analysis_data['content_desc'].str.contains(element_name, case=False, na=False)) |
                    (self.analysis_data['adaptive_name'].str.contains(element_name, case=False, na=False))
                ]

                if not element_matches.empty:
                    # Return the accessibility_id or content_desc
                    first_match = element_matches.iloc[0]
                    if 'accessibility_id' in first_match and pd.notna(first_match['accessibility_id']):
                        return first_match['accessibility_id']
                    elif 'content_desc' in first_match and pd.notna(first_match['content_desc']):
                        return first_match['content_desc']

            # Fallback to element name if not found in analysis
            return element_name

        except Exception as e:
            logger.error(f"Failed to get accessibility ID for {element_name}: {e}")
            return element_name

    def _validate_element_exists_in_analysis(self, element_name: str) -> bool:
        """Validate that an element exists in the analysis data"""
        try:
            if hasattr(self, 'analysis_data') and self.analysis_data is not None:
                # Search for element in analysis data
                element_matches = self.analysis_data[
                    (self.analysis_data['text'].str.contains(element_name, case=False, na=False)) |
                    (self.analysis_data['content_desc'].str.contains(element_name, case=False, na=False)) |
                    (self.analysis_data['adaptive_name'].str.contains(element_name, case=False, na=False)) |
                    (self.analysis_data['unique_description'].str.contains(element_name, case=False, na=False))
                ]
                return not element_matches.empty
            return False
        except Exception as e:
            logger.warning(f"Failed to validate element existence for {element_name}: {e}")
            return False

    def _get_available_elements_summary(self) -> str:
        """Get a summary of available elements from analysis data for user guidance"""
        try:
            if hasattr(self, 'analysis_data') and self.analysis_data is not None:
                # Get unique clickable elements with meaningful text
                clickable_elements = []

                # Filter for elements with actual text content (not empty or error messages)
                for _, row in self.analysis_data.iterrows():
                    text = str(row.get('text', '')).strip()
                    content_desc = str(row.get('content_desc', '')).strip()

                    # Skip empty, error, or system elements
                    if (text and text not in ['', 'null', 'nan'] and
                        not any(error in text.lower() for error in ['error', '503', 'webpage not available', 'http'])):
                        clickable_elements.append(text)
                    elif (content_desc and content_desc not in ['', 'null', 'nan'] and
                          not any(error in content_desc.lower() for error in ['error', '503', 'webpage not available', 'http'])):
                        clickable_elements.append(content_desc)

                # Remove duplicates and limit to most relevant
                unique_elements = list(set(clickable_elements))[:10]

                if unique_elements:
                    return f"Available elements in analysis: {', '.join(unique_elements)}"
                else:
                    return "No clickable elements found in analysis data (app may be in error state)"

            return "No analysis data available"
        except Exception as e:
            logger.warning(f"Failed to get available elements summary: {e}")
            return "Could not retrieve available elements"

    def _extract_feature_name_from_instructions(self, instructions: str) -> str:
        """Extract feature name from instructions"""
        try:
            # Try to extract feature name from common patterns
            patterns = [
                r'in the ([^,]+)',
                r'on the ([^,]+) screen',
                r'when in ([^,]+)',
                r'from ([^,]+) page'
            ]

            for pattern in patterns:
                match = re.search(pattern, instructions, re.IGNORECASE)
                if match:
                    feature_name = match.group(1).strip()
                    # Clean and format the feature name
                    feature_name = re.sub(r'[^\w\s]', '', feature_name)
                    feature_name = ' '.join(word.capitalize() for word in feature_name.split())
                    return feature_name

            # Default feature name if no pattern matches
            return "Custom Test Feature"

        except Exception as e:
            logger.error(f"Failed to extract feature name: {e}")
            return "Custom Test Feature"

    async def _save_feature_file_with_timestamp(self, feature_name: str, scenarios: List[str]) -> str:
        """Save enhanced feature file with proper structure and background"""
        try:
            # Create timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # Create enhanced feature file content with background
            feature_content = f"""Feature: {feature_name}
  As a mobile app user
  I want to navigate to {feature_name} and access its features
  So that I can use the application functionality effectively

  Background:
    Given I have launched the mobile application
    And I am on the main page of the application
"""

            # Add all scenarios (they should already be properly formatted)
            for scenario in scenarios:
                feature_content += scenario + "\n"

            # Create filename with timestamp
            safe_feature_name = re.sub(r'[^\w\s]', '', feature_name).replace(' ', '_').lower()
            filename = f"{safe_feature_name}_{timestamp}.feature"

            # Get output path and create filepath
            config_path = self.config.get("GHERKIN_OUTPUT_PATH")
            filepath = os.path.join(config_path, filename)

            # Ensure directory exists
            os.makedirs(self.config.get("GHERKIN_OUTPUT_PATH"), exist_ok=True)

            # Save file
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(feature_content)

            console.print(f"[green]Generated enhanced feature file: {filename}[/green]")

            return filepath

        except Exception as e:
            logger.error(f"Failed to save feature file with timestamp: {e}")
            raise

    async def _save_feature_file_without_timestamp(self, feature_name: str, scenarios: List[str]) -> str:
        """Save enhanced feature file without timestamp using exact feature name"""
        try:
            # Create enhanced feature file content with background
            feature_content = f"""Feature: {feature_name}
  As a mobile app user
  I want to navigate to {feature_name} and access its features
  So that I can use the application functionality effectively

  Background:
    Given I have launched the mobile application
    And I am on the main page of the application
"""

            # Add all scenarios (they should already be properly formatted)
            for scenario in scenarios:
                feature_content += scenario + "\n"

            # Create filename without timestamp - use exact feature name
            safe_feature_name = re.sub(r'[^\w\s]', '', feature_name).replace(' ', '_').lower()
            filename = f"{safe_feature_name}.feature"

            # Get output path and create filepath
            config_path = self.config.get("GHERKIN_OUTPUT_PATH")
            filepath = os.path.join(config_path, filename)

            # Ensure directory exists
            os.makedirs(self.config.get("GHERKIN_OUTPUT_PATH"), exist_ok=True)

            # Save file
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(feature_content)

            console.print(f"[green]Generated feature file: {filename}[/green]")

            return filepath

        except Exception as e:
            logger.error(f"Failed to save feature file without timestamp: {e}")
            raise

    async def _update_testsuite_excel(self, feature_name: str, scenarios: List[str], file_path: str, parsed_instructions: Optional[List[Dict[str, Any]]] = None) -> None:
        """Update testsuite.xlsx with new scenarios and case type information"""
        try:
            testsuite_path = "testcases/testsuite.xlsx"

            # Read existing testsuite or create new one
            try:
                df = pd.read_excel(testsuite_path, engine='openpyxl')
                # Ensure case_type column exists (add if missing for backward compatibility)
                if 'case_type' not in df.columns:
                    df['case_type'] = 'positive'  # Default for existing entries
                # Ensure Status column exists
                if 'Status' not in df.columns:
                    df['Status'] = 'Created'  # Default for existing entries
            except FileNotFoundError:
                # Create new DataFrame with required columns
                df = pd.DataFrame(columns=[
                    'Number', 'Ticket_Number', 'Feature', 'Scenario', 'case_type', 'Status', 'Link'
                ])

            # Get the next number (auto-increment)
            if len(df) > 0:
                next_number = df['Number'].max() + 1 if pd.notna(df['Number'].max()) else 1
            else:
                next_number = 1

            # Generate intelligent ticket number with package abbreviation
            timestamp = datetime.now().strftime("%Y%m%d")

            # Use cached package information for consistent ticket numbering
            package_name, package_abbrev = self._get_cached_package_info()

            # Format: [ABBREV]-YYYYMMDD-XXX (e.g., KDDMRP-20241214-001)
            ticket_number = f"{package_abbrev}-{timestamp}-{next_number:03d}"

            # Create relative path for link column (just the filename since testsuite.xlsx is in testcases/)
            relative_path = f"feature/{os.path.basename(file_path)}"

            # Extract scenario titles and determine case types
            scenario_data = []
            for i, scenario in enumerate(scenarios):
                # Extract scenario title from Gherkin content
                lines = scenario.strip().split('\n')
                scenario_title = None
                for line in lines:
                    if line.strip().startswith('Scenario:'):
                        scenario_title = line.replace('Scenario:', '').strip()
                        break

                if scenario_title:
                    # Determine case type
                    case_type = self._determine_case_type(scenario, scenario_title, parsed_instructions, i)
                    scenario_data.append({
                        'title': scenario_title,
                        'case_type': case_type
                    })

            # Check for existing scenarios to avoid duplicates
            existing_scenarios = set()
            if 'Scenario' in df.columns:
                existing_scenarios = set(df['Scenario'].dropna().tolist())

            # Add each scenario as a separate row (only if not already exists)
            new_rows = []
            for i, scenario_info in enumerate(scenario_data):
                scenario_title = scenario_info['title']

                # Skip if scenario already exists
                if scenario_title in existing_scenarios:
                    console.print(f"[yellow]Skipping duplicate scenario: {scenario_title}[/yellow]")
                    continue

                # Generate individual scenario ticket number with same abbreviation
                scenario_ticket = f"{package_abbrev}-{timestamp}-{next_number + len(new_rows):03d}"

                new_row = {
                    'Number': next_number + len(new_rows),
                    'Ticket_Number': scenario_ticket,
                    'Feature': feature_name,
                    'Scenario': scenario_title,
                    'case_type': scenario_info['case_type'],
                    'Status': 'Created',
                    'Link': relative_path
                }
                new_rows.append(new_row)
                existing_scenarios.add(scenario_title)  # Track for subsequent iterations

            # Add new rows to DataFrame
            if new_rows:
                new_df = pd.DataFrame(new_rows)
                df = pd.concat([df, new_df], ignore_index=True)

            # Ensure directory exists
            os.makedirs(os.path.dirname(testsuite_path), exist_ok=True)

            # Save updated testsuite
            df.to_excel(testsuite_path, index=False)

            # Provide detailed logging
            total_scenarios_in_file = len(df)
            new_scenarios_added = len(new_rows)
            console.print(f"[green]✅ Updated testsuite.xlsx:[/green]")
            console.print(f"[cyan]   • New scenarios added: {new_scenarios_added}[/cyan]")
            console.print(f"[cyan]   • Total scenarios in file: {total_scenarios_in_file}[/cyan]")
            console.print(f"[cyan]   • Feature: {feature_name}[/cyan]")
            console.print(f"[cyan]   • Link: {relative_path}[/cyan]")

            if new_scenarios_added == 0:
                console.print(f"[yellow]⚠️ No new scenarios added (all scenarios already exist)[/yellow]")
            else:
                console.print(f"[green]🎉 Successfully added {new_scenarios_added} new scenarios![/green]")

        except Exception as e:
            logger.error(f"Failed to update testsuite.xlsx: {e}")
            # Don't raise here as this is not critical for the main functionality
            console.print(f"[yellow]Warning: Could not update testsuite.xlsx: {e}[/yellow]")

    def _determine_case_type(self, scenario: str, scenario_title: str, parsed_instructions: Optional[List[Dict[str, Any]]], scenario_index: int) -> str:
        """Determine the case type (positive/negative) for a scenario"""
        try:
            # For custom instructions, determine case type based on scenario content and title
            # rather than relying on parsed_instructions index (since one instruction generates multiple scenarios)
            scenario_lower = scenario.lower()
            title_lower = scenario_title.lower()

            # Enhanced negative case indicators for better detection
            negative_indicators = [
                'should not', 'should not appear', 'should not be', 'should not exist',
                'error', 'fail', 'invalid', 'incorrect', 'wrong', 'missing',
                'handle error', 'gracefully', 'validation error', 'exception',
                'negative case', 'negative test', 'error handling', 'boundary',
                'handle invalid', 'invalid input', 'invalid data', 'edge case',
                'rapidly tap', 'multiple times', 'should not crash', 'boundary conditions',
                'stress testing', 'concurrent access', 'memory constraint', 'performance edge',
                'device limitation', 'data corruption', 'timeout', 'resource exhaustion',
                'malformed data', 'session expiry', 'network error', 'permission denied',
                'low battery', 'low storage', 'poor network', 'corrupted data',
                'simulate', 'trigger', 'exhaust', 'overload', 'overwhelm',
                'if the text', 'is not displayed', 'handle the missing', 'not found'
            ]

            # Check for positive case indicators
            positive_indicators = [
                'should appear', 'should be', 'should exist', 'should see',
                'successful', 'valid', 'correct', 'working', 'functional',
                'positive case', 'positive test', 'happy path', 'navigate to',
                'access', 'verify', 'with verification', 'navigation elements'
            ]

            # Count indicators in both scenario content and title
            negative_count = sum(1 for indicator in negative_indicators
                               if indicator in scenario_lower or indicator in title_lower)
            positive_count = sum(1 for indicator in positive_indicators
                               if indicator in scenario_lower or indicator in title_lower)

            # Determine case type based on indicators
            if negative_count > positive_count:
                return 'negative'
            elif positive_count > 0:
                return 'positive'
            else:
                # Default to positive if no clear indicators
                return 'positive'

        except Exception as e:
            logger.error(f"Failed to determine case type: {e}")
            # Default to positive in case of error
            return 'positive'
            
    # Using the implementation from line 1868 instead of duplicating the method

    def _detect_case_type_from_instructions(self, instructions: str) -> str:
        """Enhanced detection of case type from user instructions"""
        instructions_lower = instructions.lower()

        # Explicit case type mentions
        if any(phrase in instructions_lower for phrase in ["negative case", "negative test", "error case"]):
            return "negative"
        if any(phrase in instructions_lower for phrase in ["positive case", "positive test", "happy path"]):
            return "positive"

        # Enhanced negative indicators for better detection
        negative_keywords = [
            "error", "fail", "invalid", "incorrect", "wrong", "missing", "empty",
            "should not", "cannot", "unable", "broken", "exception", "crash",
            "handle error", "validation error", "boundary", "edge case",
            "unauthorized", "forbidden", "timeout", "reject", "deny",
            "test invalid", "wrong credentials", "special characters",
            "without proper", "without permission", "access denied",
            "stress test", "concurrent", "memory constraint", "performance edge",
            "device limitation", "data corruption", "resource exhaustion",
            "malformed", "session expiry", "network error", "permission denied",
            "simulate error", "trigger failure", "exhaust resources", "overload",
            "negative scenario", "negative case", "error condition"
        ]

        # Positive indicators
        positive_keywords = [
            "success", "valid", "correct", "working", "functional", "should work",
            "can", "able", "login", "register", "submit", "save", "create",
            "update", "delete", "navigate", "click", "tap", "enter", "select"
        ]

        # Count occurrences with weighted scoring
        negative_score = 0
        positive_score = 0

        for keyword in negative_keywords:
            if keyword in instructions_lower:
                # Give higher weight to explicit negative phrases
                if keyword in ["test invalid", "wrong credentials", "cannot", "should not", "without proper"]:
                    negative_score += 2
                else:
                    negative_score += 1

        for keyword in positive_keywords:
            if keyword in instructions_lower:
                positive_score += 1

        # Determine case type with bias toward detecting negatives
        if negative_score > 0:
            return "negative"
        elif positive_score > 0:
            return "positive"
        else:
            return "positive"

    async def _validate_and_ensure_element_locators(self, scenarios: List[str]) -> Tuple[List[str], Dict[str, Any]]:
        """Validate and ensure all element locators are available for scenarios"""
        try:
            console.print("[cyan]🔍 Starting element locator validation...[/cyan]")

            validated_scenarios = []
            validation_results = {
                "total_scenarios": len(scenarios),
                "validated_scenarios": 0,
                "deleted_scenarios": 0,
                "missing_elements": [],
                "found_elements": [],
                "ai_analysis_searches": 0,
                "elements_added_to_latest": 0
            }

            # Load latest analysis file
            latest_file = await self._get_latest_analysis_file()
            if not latest_file:
                console.print("[red]❌ No analysis file found for validation[/red]")
                return scenarios, validation_results

            # Extract all element references from scenarios
            all_element_refs = self._extract_element_references_from_scenarios(scenarios)
            console.print(f"[cyan]📋 Found {len(all_element_refs)} unique element references in scenarios[/cyan]")

            # Check which elements are missing from latest file
            missing_elements = await self._check_missing_elements(all_element_refs, latest_file)

            if missing_elements:
                console.print(f"[yellow]⚠️ Found {len(missing_elements)} missing elements[/yellow]")
                validation_results["missing_elements"] = missing_elements

                # Use AI analysis to find missing elements
                found_elements = await self._find_missing_elements_with_ai_analysis(missing_elements)
                validation_results["found_elements"] = found_elements
                validation_results["ai_analysis_searches"] = len(missing_elements)

                # Add found elements to latest file
                if found_elements:
                    added_count = await self._add_elements_to_latest_file(found_elements, latest_file)
                    validation_results["elements_added_to_latest"] = added_count
                    console.print(f"[green]✅ Added {added_count} elements to latest analysis file[/green]")

            # Validate each scenario
            for i, scenario in enumerate(scenarios):
                scenario_elements = self._extract_element_references_from_scenario(scenario)
                scenario_valid = await self._validate_scenario_elements(scenario_elements, latest_file)

                if scenario_valid:
                    validated_scenarios.append(scenario)
                    validation_results["validated_scenarios"] += 1
                else:
                    console.print(f"[red]❌ Deleting scenario {i+1} - missing required elements[/red]")
                    validation_results["deleted_scenarios"] += 1

                    # Log why scenario was deleted
                    missing_in_scenario = [elem for elem in scenario_elements if elem in missing_elements]
                    console.print(f"[red]   Missing elements: {missing_in_scenario}[/red]")

            console.print(f"[green]✅ Validation complete: {validation_results['validated_scenarios']}/{validation_results['total_scenarios']} scenarios validated[/green]")

            return validated_scenarios, validation_results

        except Exception as e:
            logger.error(f"Element validation failed: {e}")
            return scenarios, validation_results

    def _extract_element_references_from_scenarios(self, scenarios: List[str]) -> List[str]:
        """Extract all element references from Gherkin scenarios"""
        try:
            element_refs = set()

            for scenario in scenarios:
                scenario_refs = self._extract_element_references_from_scenario(scenario)
                element_refs.update(scenario_refs)

            return list(element_refs)

        except Exception as e:
            logger.error(f"Failed to extract element references: {e}")
            return []

    def _extract_element_references_from_scenario(self, scenario: str) -> List[str]:
        """Extract element references from a single scenario"""
        try:
            element_refs = []

            # Common patterns for element references in Gherkin steps
            patterns = [
                r'click on "([^"]+)"',
                r'tap on "([^"]+)"',
                r'I should see "([^"]+)"',
                r'enter "([^"]+)" in "([^"]+)"',
                r'using accessibility id "([^"]+)"',
                r'button "([^"]+)"',
                r'element "([^"]+)"',
                r'text "([^"]+)"',
                r'field "([^"]+)"',
                r'option "([^"]+)"'
            ]

            for pattern in patterns:
                matches = re.findall(pattern, scenario, re.IGNORECASE)
                for match in matches:
                    if isinstance(match, tuple):
                        # For patterns with multiple groups, take all groups
                        element_refs.extend([m for m in match if m.strip()])
                    else:
                        element_refs.append(match.strip())

            # Remove duplicates and filter out common words
            filtered_refs = []
            common_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}

            for ref in element_refs:
                if ref.lower() not in common_words and len(ref) > 2:
                    filtered_refs.append(ref)

            return list(set(filtered_refs))

        except Exception as e:
            logger.error(f"Failed to extract element references from scenario: {e}")
            return []

    async def _check_missing_elements(self, element_refs: List[str], latest_file: str) -> List[str]:
        """Check which elements are missing from the latest analysis file"""
        try:
            missing_elements = []

            # Load latest analysis data
            with open(latest_file, 'r', encoding='utf-8') as f:
                analysis_data = json.load(f)

            # Get elements from analysis data
            elements = analysis_data.get('elements', [])
            if not elements:
                elements = analysis_data.get('analysis_data', [])

            # Create a set of all available element texts/descriptions for fast lookup
            available_elements = set()
            for element in elements:
                # Add various element identifiers
                text = (element.get('text') or '').strip()
                content_desc = (element.get('content_desc') or '').strip()
                accessibility_id = (element.get('accessibility_id') or '').strip()
                element_name = (element.get('Element_Name') or '').strip()

                if text:
                    available_elements.add(text.lower())
                if content_desc:
                    available_elements.add(content_desc.lower())
                if accessibility_id:
                    available_elements.add(accessibility_id.lower())
                if element_name:
                    available_elements.add(element_name.lower())

            # Check each element reference
            for element_ref in element_refs:
                element_ref_lower = element_ref.lower()

                # Check exact match
                if element_ref_lower not in available_elements:
                    # Check partial match
                    found_partial = False
                    for available in available_elements:
                        if element_ref_lower in available or available in element_ref_lower:
                            found_partial = True
                            break

                    if not found_partial:
                        missing_elements.append(element_ref)

            return missing_elements

        except Exception as e:
            logger.error(f"Failed to check missing elements: {e}")
            return element_refs  # Return all as missing if check fails

    async def _find_missing_elements_with_ai_analysis(self, missing_elements: List[str]) -> List[Dict[str, Any]]:
        """Use AI analysis to find missing elements"""
        try:
            console.print(f"[cyan]🤖 Using AI analysis to find {len(missing_elements)} missing elements...[/cyan]")
            found_elements = []

            # Import mobile analyzer
            try:
                from mobile.analyzer import MobileAnalyzer
                analyzer = MobileAnalyzer()

                # For each missing element, perform targeted analysis
                for element_text in missing_elements:
                    console.print(f"[yellow]🔍 Searching for element: '{element_text}'[/yellow]")

                    # Use AI analysis to search for the specific element
                    search_result = await self._search_element_with_ai_analysis(analyzer, element_text)

                    if search_result:
                        found_elements.append(search_result)
                        console.print(f"[green]✅ Found element: '{element_text}'[/green]")
                    else:
                        console.print(f"[red]❌ Element not found: '{element_text}'[/red]")

                return found_elements

            except ImportError as e:
                console.print(f"[red]❌ Could not import MobileAnalyzer: {e}[/red]")
                return []

        except Exception as e:
            logger.error(f"Failed to find missing elements with AI analysis: {e}")
            return []

    async def _search_element_with_ai_analysis(self, analyzer, element_text: str) -> Optional[Dict[str, Any]]:
        """Search for a specific element using AI analysis"""
        try:
            # Use comprehensive search methods available in the analyzer
            # This could include screen scanning, element detection, etc.

            # For now, we'll simulate a comprehensive search
            # In a real implementation, this would use the analyzer's capabilities

            # Try to find the element using various analysis methods
            search_methods = [
                self._search_in_current_screen,
                self._search_in_navigation_elements,
                self._search_in_interactive_elements,
                self._search_with_ai_classification
            ]

            for search_method in search_methods:
                try:
                    result = await search_method(analyzer, element_text)
                    if result:
                        return result
                except Exception as e:
                    logger.debug(f"Search method failed: {e}")
                    continue

            return None

        except Exception as e:
            logger.error(f"Failed to search element with AI analysis: {e}")
            return None

    async def _search_in_current_screen(self, analyzer, element_text: str) -> Optional[Dict[str, Any]]:
        """Search for element in current screen"""
        try:
            # Simulate comprehensive screen analysis
            # In real implementation, this would use analyzer's screen scanning capabilities
            console.print(f"[dim]🔍 Searching current screen for: {element_text}[/dim]")

            # For now, return None to indicate not found
            # Real implementation would use analyzer.analyze_current_screen() or similar
            return None

        except Exception as e:
            logger.debug(f"Current screen search failed: {e}")
            return None

    async def _search_in_navigation_elements(self, analyzer, element_text: str) -> Optional[Dict[str, Any]]:
        """Search for element in navigation elements"""
        try:
            console.print(f"[dim]🧭 Searching navigation elements for: {element_text}[/dim]")

            # For now, return None to indicate not found
            # Real implementation would use analyzer's navigation analysis
            return None

        except Exception as e:
            logger.debug(f"Navigation search failed: {e}")
            return None

    async def _search_in_interactive_elements(self, analyzer, element_text: str) -> Optional[Dict[str, Any]]:
        """Search for element in interactive elements"""
        try:
            console.print(f"[dim]🎯 Searching interactive elements for: {element_text}[/dim]")

            # For now, return None to indicate not found
            # Real implementation would use analyzer's interactive element detection
            return None

        except Exception as e:
            logger.debug(f"Interactive elements search failed: {e}")
            return None

    async def _search_with_ai_classification(self, analyzer, element_text: str) -> Optional[Dict[str, Any]]:
        """Search for element using AI classification"""
        try:
            console.print(f"[dim]🤖 Using AI classification to find: {element_text}[/dim]")

            # For now, return None to indicate not found
            # Real implementation would use analyzer's AI classification capabilities
            return None

        except Exception as e:
            logger.debug(f"AI classification search failed: {e}")
            return None

    async def _add_elements_to_latest_file(self, found_elements: List[Dict[str, Any]], latest_file: str) -> int:
        """Add found elements to the latest analysis file"""
        try:
            if not found_elements:
                return 0

            console.print(f"[cyan]💾 Adding {len(found_elements)} elements to latest file...[/cyan]")

            # Load current analysis data
            with open(latest_file, 'r', encoding='utf-8') as f:
                analysis_data = json.load(f)

            # Get existing elements
            elements = analysis_data.get('elements', [])
            if not elements:
                elements = analysis_data.get('analysis_data', [])
                key = 'analysis_data'
            else:
                key = 'elements'

            # Add new elements
            elements.extend(found_elements)
            analysis_data[key] = elements

            # Update metadata
            if 'metadata' not in analysis_data:
                analysis_data['metadata'] = {}

            analysis_data['metadata']['last_updated'] = datetime.now().isoformat()
            analysis_data['metadata']['elements_added_by_gherkin_validation'] = len(found_elements)

            # Save updated file
            with open(latest_file, 'w', encoding='utf-8') as f:
                json.dump(analysis_data, f, indent=2, ensure_ascii=False)

            console.print(f"[green]✅ Successfully added {len(found_elements)} elements to {os.path.basename(latest_file)}[/green]")
            return len(found_elements)

        except Exception as e:
            logger.error(f"Failed to add elements to latest file: {e}")
            return 0

    async def _validate_scenario_elements(self, scenario_elements: List[str], latest_file: str) -> bool:
        """Validate that all elements in a scenario are available"""
        try:
            if not scenario_elements:
                return True  # No elements to validate

            # Check if all elements are available in the latest file
            missing_elements = await self._check_missing_elements(scenario_elements, latest_file)

            # Scenario is valid if no elements are missing
            return len(missing_elements) == 0

        except Exception as e:
            logger.error(f"Failed to validate scenario elements: {e}")
            return False
